@echo off
setlocal enabledelayedexpansion
chcp 65001 >nul
cls

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                    مشغل البرنامج النهائي                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 جاري البحث عن Python في النظام...
echo.

REM محاولة 1: python في PATH
echo [1/6] فحص python في PATH...
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على python في PATH
    goto :run_python
)

REM محاولة 2: py launcher
echo [2/6] فحص py launcher...
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على py launcher
    set PYTHON_CMD=py
    goto :run_python
)

REM محاولة 3: python3
echo [3/6] فحص python3...
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على python3
    set PYTHON_CMD=python3
    goto :run_python
)

REM محاولة 4: البحث في مجلدات المستخدم
echo [4/6] البحث في مجلدات المستخدم...
for /d %%d in ("C:\Users\<USER>\AppData\Local\Programs\Python\Python*") do (
    if exist "%%d\python.exe" (
        echo ✅ تم العثور على Python في: %%d
        set PYTHON_CMD="%%d\python.exe"
        goto :run_python
    )
)

REM محاولة 5: البحث في Program Files
echo [5/6] البحث في Program Files...
for /d %%d in ("C:\Program Files\Python*") do (
    if exist "%%d\python.exe" (
        echo ✅ تم العثور على Python في: %%d
        set PYTHON_CMD="%%d\python.exe"
        goto :run_python
    )
)

for /d %%d in ("C:\Program Files (x86)\Python*") do (
    if exist "%%d\python.exe" (
        echo ✅ تم العثور على Python في: %%d
        set PYTHON_CMD="%%d\python.exe"
        goto :run_python
    )
)

REM محاولة 6: البحث في C:\
echo [6/6] البحث في الجذر...
for /d %%d in ("C:\Python*") do (
    if exist "%%d\python.exe" (
        echo ✅ تم العثور على Python في: %%d
        set PYTHON_CMD="%%d\python.exe"
        goto :run_python
    )
)

REM إذا لم يتم العثور على Python
goto :no_python

:run_python
echo.
echo 🚀 جاري تشغيل تطبيق سطح المكتب...
echo.

REM تجربة النسخة المستقلة أولاً
if exist "ice_cream_standalone.py" (
    echo تشغيل النسخة المستقلة...
    %PYTHON_CMD% ice_cream_standalone.py
    if not errorlevel 1 (
        echo ✅ تم تشغيل البرنامج بنجاح!
        goto :success
    )
)

REM تجربة النسخة المتقدمة
if exist "desktop_app.py" (
    echo تشغيل النسخة المتقدمة...
    %PYTHON_CMD% desktop_app.py
    if not errorlevel 1 (
        echo ✅ تم تشغيل البرنامج بنجاح!
        goto :success
    )
)

REM تجربة المشغل
if exist "start_desktop.py" (
    echo تشغيل المشغل...
    %PYTHON_CMD% start_desktop.py
    if not errorlevel 1 (
        echo ✅ تم تشغيل البرنامج بنجاح!
        goto :success
    )
)

echo ❌ فشل في تشغيل تطبيق سطح المكتب
goto :try_alternatives

:no_python
echo ❌ لم يتم العثور على Python!
echo.

:try_alternatives
echo.
echo 🔄 جاري تجربة البدائل...
echo.

REM تجربة النسخة التجريبية
if exist "ice_cream_cashier.html" (
    echo 🌐 فتح النسخة التجريبية في المتصفح...
    start "" "ice_cream_cashier.html"
    echo ✅ تم فتح النسخة التجريبية!
    echo.
    echo 💡 النسخة التجريبية تعمل الآن في المتصفح
    echo    يمكنك استخدام جميع وظائف الكاشير الأساسية
    goto :end
)

echo ❌ لم يتم العثور على أي نسخة قابلة للتشغيل!
echo.
echo 💡 الحلول:
echo.
echo 1️⃣ لتطبيق سطح المكتب:
echo    • ثبت Python من: https://www.python.org/downloads/
echo    • تأكد من تحديد "Add Python to PATH"
echo    • أعد تشغيل هذا الملف
echo.
echo 2️⃣ للنسخة التجريبية:
echo    • تأكد من وجود ملف ice_cream_cashier.html
echo    • انقر عليه نقراً مزدوجاً
echo.
goto :end

:success
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🎉 تم تشغيل برنامج كاشير الآيس كريم بنجاح!
echo ═══════════════════════════════════════════════════════════════

:end
echo.
echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
echo.
pause
