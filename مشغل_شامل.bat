@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                      المشغل الشامل                          ║
echo ║                   الريال العماني (ر.ع)                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 اختر الإصدار الذي تريد تشغيله:
echo.

echo ═══════════════════════════════════════════════════════════════
echo 🌐 الإصدارات المتاحة:
echo ═══════════════════════════════════════════════════════════════
echo.

echo 1️⃣  النسخة التجريبية (HTML) - يعمل فوراً
echo     📁 ice_cream_cashier.html
echo     ✅ لا يحتاج Python
echo     ✅ يعمل في المتصفح
echo.

echo 2️⃣  النسخة المستقلة (Python) - الأفضل
echo     📁 كاشير_مستقل_تماما.py
echo     ✅ لا يحتاج Flask
echo     ✅ واجهة سطح مكتب
echo.

echo 3️⃣  النسخة المحسنة (Python)
echo     📁 ice_cream_exe.py
echo     ✅ مميزات إضافية
echo     ✅ واجهة محسنة
echo.

echo 4️⃣  إدارة النسخ الاحتياطية
echo     📁 نسخة_احتياطية.py
echo     ✅ حفظ واستعادة البيانات
echo.

echo 5️⃣  الإعدادات المتقدمة
echo     📁 إعدادات_متقدمة.py
echo     ✅ تخصيص المنتجات والأسعار
echo.

echo 6️⃣  إنشاء ملف تنفيذي (.exe)
echo     📁 إنشاء_exe_سريع.bat - ملف .exe بسيط وسريع
echo     📁 إنشاء_ملف_تنفيذي.bat - خيارات متقدمة
echo     ✅ ملف مستقل للتوزيع
echo.

echo 7️⃣  إنشاء ملف تثبيت احترافي (Setup 1.0.0.exe)
echo     📁 إنشاء_Setup_1.0.0.bat
echo     ✅ ملف Setup.exe احترافي مع installer
echo.

echo 8️⃣  تثبيت Python الصحيح
echo     📁 تثبيت_Python_الصحيح.bat
echo     ✅ حل مشاكل Python
echo.

echo 9️⃣  عرض جميع الملفات والتوثيق
echo.

echo 0️⃣  خروج
echo.

echo ═══════════════════════════════════════════════════════════════
set /p choice="اختر رقم الخيار (0-9): "

if "%choice%"=="1" goto :html_version
if "%choice%"=="2" goto :standalone_version
if "%choice%"=="3" goto :enhanced_version
if "%choice%"=="4" goto :backup_manager
if "%choice%"=="5" goto :advanced_settings
if "%choice%"=="6" goto :create_exe
if "%choice%"=="7" goto :create_setup
if "%choice%"=="8" goto :install_python
if "%choice%"=="9" goto :show_files
if "%choice%"=="0" goto :exit

echo ❌ خيار غير صحيح!
pause
goto :start

:html_version
echo.
echo 🌐 تشغيل النسخة التجريبية...
echo ✅ تعمل فوراً في المتصفح
echo 💰 العملة: الريال العماني (ر.ع)
echo.
if exist "ice_cream_cashier.html" (
    start "" "ice_cream_cashier.html"
    echo ✅ تم فتح النسخة التجريبية في المتصفح!
) else (
    echo ❌ لم يتم العثور على ملف ice_cream_cashier.html
)
goto :end

:standalone_version
echo.
echo 🖥️ تشغيل النسخة المستقلة...
echo 💰 العملة: الريال العماني (ر.ع)
echo.
call "تشغيل_مستقل.bat"
goto :end

:enhanced_version
echo.
echo ⭐ تشغيل النسخة المحسنة...
echo 💰 العملة: الريال العماني (ر.ع)
echo.
call "تشغيل_البرنامج_النهائي.bat"
goto :end

:backup_manager
echo.
echo 💾 تشغيل إدارة النسخ الاحتياطية...
echo.
python "نسخة_احتياطية.py" 2>nul
if errorlevel 1 (
    py "نسخة_احتياطية.py" 2>nul
    if errorlevel 1 (
        echo ❌ Python غير متاح
        echo 💡 استخدم النسخة التجريبية أو ثبت Python
    )
)
goto :end

:advanced_settings
echo.
echo ⚙️ تشغيل الإعدادات المتقدمة...
echo.
python "إعدادات_متقدمة.py" 2>nul
if errorlevel 1 (
    py "إعدادات_متقدمة.py" 2>nul
    if errorlevel 1 (
        echo ❌ Python غير متاح
        echo 💡 استخدم النسخة التجريبية أو ثبت Python
    )
)
goto :end

:create_exe
echo.
echo 📦 إنشاء ملف تنفيذي...
echo.
call "إنشاء_ملف_تنفيذي.bat"
goto :end

:create_setup
echo.
echo 📦 إنشاء ملف تثبيت احترافي Setup 1.0.0.exe...
echo.
call "إنشاء_Setup_1.0.0.bat"
goto :end

:install_python
echo.
echo 🔧 دليل تثبيت Python...
echo.
call "تثبيت_Python_الصحيح.bat"
goto :end

:show_files
echo.
echo 📁 جميع الملفات المتاحة:
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🌐 ملفات التشغيل الفوري:
if exist "ice_cream_cashier.html" (echo ✅ ice_cream_cashier.html - النسخة التجريبية) else (echo ❌ ice_cream_cashier.html - مفقود)
if exist "تشغيل_فوري.pyw" (echo ✅ تشغيل_فوري.pyw - تشغيل صامت) else (echo ❌ تشغيل_فوري.pyw - مفقود)
echo.

echo 🖥️ تطبيقات سطح المكتب:
if exist "كاشير_مستقل_تماما.py" (echo ✅ كاشير_مستقل_تماما.py - النسخة المستقلة) else (echo ❌ كاشير_مستقل_تماما.py - مفقود)
if exist "ice_cream_exe.py" (echo ✅ ice_cream_exe.py - النسخة المحسنة) else (echo ❌ ice_cream_exe.py - مفقود)
if exist "ice_cream_standalone.py" (echo ✅ ice_cream_standalone.py - النسخة الأساسية) else (echo ❌ ice_cream_standalone.py - مفقود)
if exist "desktop_app.py" (echo ✅ desktop_app.py - النسخة المتقدمة) else (echo ❌ desktop_app.py - مفقود)
if exist "app.py" (echo ✅ app.py - نسخة Flask) else (echo ❌ app.py - مفقود)
echo.

echo 🛠️ أدوات إضافية:
if exist "نسخة_احتياطية.py" (echo ✅ نسخة_احتياطية.py - إدارة النسخ الاحتياطية) else (echo ❌ نسخة_احتياطية.py - مفقود)
if exist "إعدادات_متقدمة.py" (echo ✅ إعدادات_متقدمة.py - تخصيص البرنامج) else (echo ❌ إعدادات_متقدمة.py - مفقود)
echo.

echo 🚀 ملفات التشغيل:
if exist "مشغل_شامل.bat" (echo ✅ مشغل_شامل.bat - هذا الملف) else (echo ❌ مشغل_شامل.bat - مفقود)
if exist "تشغيل_مستقل.bat" (echo ✅ تشغيل_مستقل.bat - للنسخة المستقلة) else (echo ❌ تشغيل_مستقل.bat - مفقود)
if exist "تشغيل_البرنامج_النهائي.bat" (echo ✅ تشغيل_البرنامج_النهائي.bat - تشغيل ذكي) else (echo ❌ تشغيل_البرنامج_النهائي.bat - مفقود)
if exist "إنشاء_ملف_تنفيذي.bat" (echo ✅ إنشاء_ملف_تنفيذي.bat - إنشاء exe متقدم) else (echo ❌ إنشاء_ملف_تنفيذي.bat - مفقود)
if exist "إنشاء_exe_سريع.bat" (echo ✅ إنشاء_exe_سريع.bat - إنشاء exe سريع) else (echo ❌ إنشاء_exe_سريع.bat - مفقود)
if exist "إنشاء_Setup_1.0.0.bat" (echo ✅ إنشاء_Setup_1.0.0.bat - إنشاء Setup احترافي) else (echo ❌ إنشاء_Setup_1.0.0.bat - مفقود)
if exist "تثبيت_Python_الصحيح.bat" (echo ✅ تثبيت_Python_الصحيح.bat - حل مشاكل Python) else (echo ❌ تثبيت_Python_الصحيح.bat - مفقود)
echo.

echo 📚 ملفات التوثيق:
if exist "README_النهائي.txt" (echo ✅ README_النهائي.txt - دليل شامل) else (echo ❌ README_النهائي.txt - مفقود)
if exist "الملخص_النهائي.txt" (echo ✅ الملخص_النهائي.txt - ملخص المشروع) else (echo ❌ الملخص_النهائي.txt - مفقود)
if exist "دليل_إنشاء_الملف_التنفيذي.txt" (echo ✅ دليل_إنشاء_الملف_التنفيذي.txt - دليل EXE) else (echo ❌ دليل_إنشاء_الملف_التنفيذي.txt - مفقود)
if exist "كيفية_التشغيل.txt" (echo ✅ كيفية_التشغيل.txt - دليل سريع) else (echo ❌ كيفية_التشغيل.txt - مفقود)
echo.

echo ⚙️ ملفات الإعداد:
if exist "setup.py" (echo ✅ setup.py - إعداد cx_Freeze) else (echo ❌ setup.py - مفقود)
if exist "requirements_build.txt" (echo ✅ requirements_build.txt - متطلبات البناء) else (echo ❌ requirements_build.txt - مفقود)
echo.

echo 💾 ملفات البيانات:
if exist "cashier_data.json" (echo ✅ cashier_data.json - بيانات البرنامج) else (echo ℹ️ cashier_data.json - سيتم إنشاؤه عند الاستخدام)
if exist "settings.json" (echo ✅ settings.json - إعدادات مخصصة) else (echo ℹ️ settings.json - سيتم إنشاؤه عند الحاجة)
if exist "products.json" (echo ✅ products.json - منتجات مخصصة) else (echo ℹ️ products.json - سيتم إنشاؤه عند الحاجة)
echo.

echo ═══════════════════════════════════════════════════════════════
echo 💡 التوصية:
echo ═══════════════════════════════════════════════════════════════
echo.
echo 🌟 للاستخدام الفوري: اختر الخيار 1 (النسخة التجريبية)
echo 🖥️ لتطبيق سطح المكتب: اختر الخيار 2 (النسخة المستقلة)
echo 🔧 لحل مشاكل Python: اختر الخيار 7
echo.
goto :end

:exit
echo.
echo 👋 شكراً لاستخدام برنامج كاشير الآيس كريم!
echo 🍦 نتمنى لك تجارة رابحة بالريال العماني! 🇴🇲
echo.
exit /b 0

:end
echo.
echo ═══════════════════════════════════════════════════════════════
echo هل تريد العودة للقائمة الرئيسية؟ (y/n)
set /p return_choice="اختر (y للنعم، n للخروج): "

if /i "%return_choice%"=="y" (
    cls
    goto :start
) else (
    goto :exit
)

:start
goto :0
