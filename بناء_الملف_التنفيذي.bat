@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                    بناء الملف التنفيذي                       ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 جاري إعداد بيئة البناء...
echo.

REM تثبيت cx_Freeze
echo [1/4] تثبيت cx_Freeze...
pip install cx_Freeze
if errorlevel 1 (
    echo ❌ فشل في تثبيت cx_Freeze
    echo جرب: pip install --user cx_Freeze
    pause
    exit /b 1
)

echo ✅ تم تثبيت cx_Freeze بنجاح
echo.

REM إنشاء مجلد البناء
echo [2/4] إنشاء مجلد البناء...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"

echo [3/4] بناء الملف التنفيذي...
python setup.py build
if errorlevel 1 (
    echo ❌ فشل في بناء الملف التنفيذي
    pause
    exit /b 1
)

echo ✅ تم بناء الملف التنفيذي بنجاح!
echo.

echo [4/4] إنشاء حزمة التوزيع...

REM إنشاء مجلد التوزيع
if not exist "package" mkdir "package"
if exist "package\*" del /q "package\*"

REM نسخ الملفات
xcopy "build\exe.win-amd64-3.13\*" "package\" /E /I /Y
copy "كيفية_التشغيل.txt" "package\"
copy "ice_cream_cashier.html" "package\"

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🎉 تم إنشاء الملف التنفيذي بنجاح!
echo ═══════════════════════════════════════════════════════════════
echo.
echo 📁 الملفات موجودة في مجلد: package
echo 🚀 الملف التنفيذي: كاشير_الآيس_كريم.exe
echo.
echo 💡 يمكنك الآن:
echo    1. نسخ مجلد package إلى أي جهاز آخر
echo    2. تشغيل كاشير_الآيس_كريم.exe مباشرة
echo    3. لا يحتاج تثبيت Python أو أي مكتبات
echo.

pause
