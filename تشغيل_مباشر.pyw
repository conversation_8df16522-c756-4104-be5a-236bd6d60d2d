#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مباشر لبرنامج كاشير الآيس كريم
ملف .pyw يعمل بالنقر المزدوج بدون نافذة أوامر
"""

import sys
import os
import webbrowser
import subprocess
from pathlib import Path

def show_message(title, message):
    """عرض رسالة للمستخدم"""
    try:
        import tkinter as tk
        from tkinter import messagebox
        
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        messagebox.showinfo(title, message)
        root.destroy()
    except:
        print(f"{title}: {message}")

def run_desktop_app():
    """تشغيل تطبيق سطح المكتب"""
    try:
        # تجربة النسخة المستقلة
        if os.path.exists('ice_cream_standalone.py'):
            import ice_cream_standalone
            app = ice_cream_standalone.SimpleIceCreamCashier()
            app.run()
            return True
    except Exception as e:
        pass
    
    try:
        # تجربة النسخة المتقدمة
        if os.path.exists('desktop_app.py'):
            import desktop_app
            app = desktop_app.IceCreamCashier()
            app.run()
            return True
    except Exception as e:
        pass
    
    return False

def run_html_version():
    """تشغيل النسخة التجريبية"""
    try:
        html_file = Path('ice_cream_cashier.html')
        if html_file.exists():
            webbrowser.open(html_file.absolute().as_uri())
            return True
    except:
        pass
    return False

def main():
    """الدالة الرئيسية"""
    try:
        # محاولة تشغيل تطبيق سطح المكتب
        if run_desktop_app():
            return
        
        # إذا فشل، تشغيل النسخة التجريبية
        if run_html_version():
            show_message("كاشير الآيس كريم", 
                        "تم فتح النسخة التجريبية في المتصفح!\n\n" +
                        "للحصول على تطبيق سطح المكتب الكامل:\n" +
                        "تأكد من وجود ملف ice_cream_standalone.py")
            return
        
        # إذا فشل كل شيء
        show_message("خطأ", 
                    "لم يتم العثور على أي نسخة من البرنامج!\n\n" +
                    "تأكد من وجود الملفات التالية:\n" +
                    "• ice_cream_standalone.py\n" +
                    "• ice_cream_cashier.html")
        
    except Exception as e:
        show_message("خطأ", f"حدث خطأ في تشغيل البرنامج:\n{str(e)}")

if __name__ == "__main__":
    main()
