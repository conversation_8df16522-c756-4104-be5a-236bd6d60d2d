@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                  النسخة المستقلة تماماً                     ║
echo ║                   الريال العماني (ر.ع)                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 هذه النسخة تعمل مع Python الأساسي فقط
echo 💰 العملة: الريال العماني (ر.ع)
echo ✅ لا تحتاج Flask أو أي مكتبات خارجية
echo.

echo 🔍 جاري البحث عن Python...

REM محاولة تشغيل Python بطرق مختلفة
echo [1/4] تجربة python...
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على python
    echo 🚀 جاري تشغيل البرنامج...
    echo.
    python "كاشير_مستقل_تماما.py"
    goto :end
)

echo [2/4] تجربة py...
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على py
    echo 🚀 جاري تشغيل البرنامج...
    echo.
    py "كاشير_مستقل_تماما.py"
    goto :end
)

echo [3/4] تجربة المسار المباشر...
if exist "C:\Python313\python.exe" (
    echo ✅ تم العثور على Python في C:\Python313\
    echo 🚀 جاري تشغيل البرنامج...
    echo.
    "C:\Python313\python.exe" "كاشير_مستقل_تماما.py"
    goto :end
)

echo [4/4] البحث في مجلدات أخرى...
for /d %%d in ("C:\Python*") do (
    if exist "%%d\python.exe" (
        echo ✅ تم العثور على Python في: %%d
        echo 🚀 جاري تشغيل البرنامج...
        echo.
        "%%d\python.exe" "كاشير_مستقل_تماما.py"
        goto :end
    )
)

REM إذا لم يتم العثور على Python
echo ❌ لم يتم العثور على Python!
echo.
echo 💡 الحلول:
echo.
echo 1️⃣ تثبيت Python:
echo    • حمل من: https://www.python.org/downloads/
echo    • تأكد من تحديد "Add Python to PATH"
echo    • أعد تشغيل الكمبيوتر
echo.
echo 2️⃣ النسخة التجريبية:
echo    • انقر نقراً مزدوجاً على: ice_cream_cashier.html
echo    • تعمل فوراً في المتصفح
echo.
goto :end

:end
echo.
echo ═══════════════════════════════════════════════════════════════
echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
echo ═══════════════════════════════════════════════════════════════
echo.
pause
