#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل فوري لبرنامج كاشير الآيس كريم
يعمل بنقرة واحدة بدون نافذة أوامر
"""

import sys
import os

# إضافة المجلد الحالي لمسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    # محاولة تشغيل النسخة المستقلة
    import tkinter as tk
    from tkinter import messagebox
    
    # تشغيل البرنامج
    exec(open('كاشير_مستقل_تماما.py', encoding='utf-8').read())
    
except ImportError as e:
    # إذا فشل tkinter
    try:
        import tkinter.messagebox as mb
        mb.showerror("خطأ", f"مكتبة tkinter غير متاحة:\n{e}\n\nيرجى تثبيت Python مع tkinter")
    except:
        print(f"خطأ: {e}")
        print("يرجى تثبيت Python مع tkinter")
        input("اضغط Enter للخروج...")

except FileNotFoundError:
    try:
        import tkinter.messagebox as mb
        mb.showerror("خطأ", "لم يتم العثور على ملف البرنامج الرئيسي")
    except:
        print("خطأ: لم يتم العثور على ملف البرنامج الرئيسي")
        input("اضغط Enter للخروج...")

except Exception as e:
    try:
        import tkinter.messagebox as mb
        mb.showerror("خطأ", f"حدث خطأ في تشغيل البرنامج:\n{e}")
    except:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")
