@echo off
chcp 65001 >nul
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                      البحث الذكي عن Python                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo جاري البحث عن Python في النظام...
echo.

REM قائمة المواقع الشائعة لـ Python
set PYTHON_PATHS[0]="C:\Python39\python.exe"
set PYTHON_PATHS[1]="C:\Python38\python.exe"
set PYTHON_PATHS[2]="C:\Python37\python.exe"
set PYTHON_PATHS[3]="C:\Python310\python.exe"
set PYTHON_PATHS[4]="C:\Python311\python.exe"
set PYTHON_PATHS[5]="C:\Python312\python.exe"
set PYTHON_PATHS[6]="C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe"
set PYTHON_PATHS[7]="C:\Users\<USER>\AppData\Local\Programs\Python\Python38\python.exe"
set PYTHON_PATHS[8]="C:\Users\<USER>\AppData\Local\Programs\Python\Python37\python.exe"
set PYTHON_PATHS[9]="C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe"
set PYTHON_PATHS[10]="C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe"
set PYTHON_PATHS[11]="C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe"
set PYTHON_PATHS[12]="C:\Program Files\Python39\python.exe"
set PYTHON_PATHS[13]="C:\Program Files\Python38\python.exe"
set PYTHON_PATHS[14]="C:\Program Files\Python37\python.exe"
set PYTHON_PATHS[15]="C:\Program Files (x86)\Python39\python.exe"
set PYTHON_PATHS[16]="C:\Program Files (x86)\Python38\python.exe"

REM البحث في المواقع الشائعة
for /L %%i in (0,1,16) do (
    call set "CURRENT_PATH=%%PYTHON_PATHS[%%i]%%"
    call set CURRENT_PATH=%%CURRENT_PATH:"=%%
    if exist "!CURRENT_PATH!" (
        echo ✅ تم العثور على Python في: !CURRENT_PATH!
        echo.
        echo جاري تشغيل تطبيق سطح المكتب...
        echo.
        "!CURRENT_PATH!" ice_cream_standalone.py
        if not errorlevel 1 (
            echo.
            echo ✅ تم تشغيل البرنامج بنجاح!
            goto :success
        ) else (
            echo ❌ فشل في تشغيل البرنامج
        )
    )
)

REM محاولة البحث في PATH
echo جاري البحث في متغير PATH...
for %%i in (python.exe) do (
    if not "%%~$PATH:i"=="" (
        echo ✅ تم العثور على Python في PATH
        echo جاري تشغيل البرنامج...
        echo.
        "%%~$PATH:i" ice_cream_standalone.py
        if not errorlevel 1 goto :success
    )
)

REM محاولة py launcher
echo جاري تجربة py launcher...
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على py launcher
    echo جاري تشغيل البرنامج...
    echo.
    py ice_cream_standalone.py
    if not errorlevel 1 goto :success
)

REM إذا فشل كل شيء
echo.
echo ❌ لم يتم العثور على Python أو فشل في التشغيل!
echo.
echo الحلول البديلة:
echo.
echo 1️⃣ النسخة التجريبية (تعمل فوراً):
echo    انقر نقراً مزدوجاً على: ice_cream_cashier.html
echo.
echo 2️⃣ إضافة Python إلى PATH:
echo    - افتح إعدادات النظام
echo    - ابحث عن "متغيرات البيئة"
echo    - أضف مجلد Python إلى PATH
echo.
echo 3️⃣ إعادة تثبيت Python:
echo    - حمل من: https://www.python.org/downloads/
echo    - تأكد من تحديد "Add Python to PATH"
echo.
goto :end

:success
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🎉 تم تشغيل برنامج كاشير الآيس كريم بنجاح!
echo ═══════════════════════════════════════════════════════════════

:end
echo.
pause
