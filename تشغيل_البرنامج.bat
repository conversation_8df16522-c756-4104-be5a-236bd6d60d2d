@echo off
chcp 65001 >nul
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                   تطبيق سطح مكتب متكامل                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo مرحباً بك في برنامج كاشير الآيس كريم!
echo.
echo المميزات:
echo ✅ واجهة سطح مكتب سهلة الاستخدام
echo ✅ إدارة المبيعات والمنتجات
echo ✅ تقارير الأرباح والخسائر
echo ✅ حفظ البيانات تلقائياً
echo.

echo جاري فحص النظام...
echo.

REM محاولة تشغيل Python بطرق مختلفة
echo [1/4] جاري البحث عن Python...
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على Python
    echo جاري تشغيل البرنامج...
    echo.
    python ice_cream_standalone.py
    goto :end
)

echo [2/4] جاري البحث عن py...
py --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على py
    echo جاري تشغيل البرنامج...
    echo.
    py ice_cream_standalone.py
    goto :end
)

echo [3/4] جاري البحث عن python3...
python3 --version >nul 2>&1
if not errorlevel 1 (
    echo ✅ تم العثور على python3
    echo جاري تشغيل البرنامج...
    echo.
    python3 ice_cream_standalone.py
    goto :end
)

echo [4/4] جاري البحث في مجلدات النظام...
for %%i in (python.exe) do (
    if not "%%~$PATH:i"=="" (
        echo ✅ تم العثور على Python في النظام
        echo جاري تشغيل البرنامج...
        echo.
        "%%~$PATH:i" ice_cream_standalone.py
        goto :end
    )
)

REM إذا لم يتم العثور على Python
echo.
echo ❌ لم يتم العثور على Python!
echo.
echo لتشغيل البرنامج، يرجى:
echo.
echo 1️⃣ تحميل Python من: https://www.python.org/downloads/
echo 2️⃣ تثبيت Python مع تحديد "Add Python to PATH"
echo 3️⃣ إعادة تشغيل هذا الملف
echo.
echo أو يمكنك استخدام النسخة التجريبية في المتصفح:
echo انقر نقراً مزدوجاً على ملف: ice_cream_cashier.html
echo.

:end
echo.
echo ═══════════════════════════════════════════════════════════════
echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
echo ═══════════════════════════════════════════════════════════════
echo.
pause
