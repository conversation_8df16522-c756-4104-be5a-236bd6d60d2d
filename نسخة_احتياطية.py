#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة النسخ الاحتياطي لبرنامج كاشير الآيس كريم
تقوم بحفظ واستعادة البيانات
"""

import json
import os
import shutil
from datetime import datetime
import tkinter as tk
from tkinter import messagebox, filedialog

class BackupManager:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔄 إدارة النسخ الاحتياطية - كاشير الآيس كريم")
        self.root.geometry("600x500")
        self.root.configure(bg='#f0f8ff')
        
        self.data_file = 'cashier_data.json'
        self.backup_folder = 'backups'
        
        # إنشاء مجلد النسخ الاحتياطية
        if not os.path.exists(self.backup_folder):
            os.makedirs(self.backup_folder)
        
        self.create_interface()
    
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # العنوان
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="🔄 إدارة النسخ الاحتياطية", 
                              font=('Arial', 18, 'bold'), 
                              fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f0f8ff')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # معلومات الملف الحالي
        info_frame = tk.LabelFrame(main_frame, text="📊 معلومات البيانات الحالية", 
                                  font=('Arial', 12, 'bold'), bg='white')
        info_frame.pack(fill='x', pady=10)
        
        self.info_label = tk.Label(info_frame, text="جاري تحميل المعلومات...", 
                                  font=('Arial', 11), bg='white')
        self.info_label.pack(pady=10)
        
        # أزرار النسخ الاحتياطي
        backup_frame = tk.LabelFrame(main_frame, text="💾 إنشاء نسخة احتياطية", 
                                    font=('Arial', 12, 'bold'), bg='white')
        backup_frame.pack(fill='x', pady=10)
        
        tk.Button(backup_frame, text="📁 نسخة احتياطية سريعة", 
                 font=('Arial', 12, 'bold'), bg='#27ae60', fg='white',
                 command=self.quick_backup).pack(pady=5, padx=10, fill='x')
        
        tk.Button(backup_frame, text="💾 نسخة احتياطية مخصصة", 
                 font=('Arial', 12, 'bold'), bg='#3498db', fg='white',
                 command=self.custom_backup).pack(pady=5, padx=10, fill='x')
        
        # أزرار الاستعادة
        restore_frame = tk.LabelFrame(main_frame, text="🔄 استعادة البيانات", 
                                     font=('Arial', 12, 'bold'), bg='white')
        restore_frame.pack(fill='x', pady=10)
        
        tk.Button(restore_frame, text="📂 استعادة من ملف", 
                 font=('Arial', 12, 'bold'), bg='#f39c12', fg='white',
                 command=self.restore_from_file).pack(pady=5, padx=10, fill='x')
        
        tk.Button(restore_frame, text="📋 عرض النسخ المتاحة", 
                 font=('Arial', 12, 'bold'), bg='#9b59b6', fg='white',
                 command=self.show_backups).pack(pady=5, padx=10, fill='x')
        
        # أدوات إضافية
        tools_frame = tk.LabelFrame(main_frame, text="🛠️ أدوات إضافية", 
                                   font=('Arial', 12, 'bold'), bg='white')
        tools_frame.pack(fill='x', pady=10)
        
        tk.Button(tools_frame, text="🗑️ مسح جميع البيانات", 
                 font=('Arial', 12, 'bold'), bg='#e74c3c', fg='white',
                 command=self.clear_all_data).pack(pady=5, padx=10, fill='x')
        
        tk.Button(tools_frame, text="📊 تصدير تقرير شامل", 
                 font=('Arial', 12, 'bold'), bg='#16a085', fg='white',
                 command=self.export_report).pack(pady=5, padx=10, fill='x')
        
        # تحديث المعلومات
        self.update_info()
    
    def update_info(self):
        """تحديث معلومات الملف الحالي"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                total_sales = data.get('total_sales', 0)
                total_expenses = data.get('total_expenses', 0)
                sales_count = len(data.get('sales_history', []))
                last_updated = data.get('last_updated', 'غير معروف')
                
                info_text = f"""📊 إحصائيات البيانات الحالية:

💰 إجمالي المبيعات: {total_sales:.3f} ر.ع
💸 إجمالي المصروفات: {total_expenses:.3f} ر.ع
🧾 عدد الفواتير: {sales_count}
📅 آخر تحديث: {last_updated[:19] if last_updated != 'غير معروف' else last_updated}
📁 حجم الملف: {os.path.getsize(self.data_file)} بايت"""
                
            else:
                info_text = "❌ لا توجد بيانات محفوظة حالياً"
            
            self.info_label.config(text=info_text)
            
        except Exception as e:
            self.info_label.config(text=f"❌ خطأ في قراءة البيانات: {str(e)}")
    
    def quick_backup(self):
        """إنشاء نسخة احتياطية سريعة"""
        try:
            if not os.path.exists(self.data_file):
                messagebox.showwarning("تحذير", "لا توجد بيانات للنسخ الاحتياطي!")
                return
            
            # إنشاء اسم الملف
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{timestamp}.json"
            backup_path = os.path.join(self.backup_folder, backup_filename)
            
            # نسخ الملف
            shutil.copy2(self.data_file, backup_path)
            
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية:\n{backup_filename}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")
    
    def custom_backup(self):
        """إنشاء نسخة احتياطية مخصصة"""
        try:
            if not os.path.exists(self.data_file):
                messagebox.showwarning("تحذير", "لا توجد بيانات للنسخ الاحتياطي!")
                return
            
            # اختيار مكان الحفظ
            backup_path = filedialog.asksaveasfilename(
                title="حفظ النسخة الاحتياطية",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                initialname=f"كاشير_نسخة_احتياطية_{datetime.now().strftime('%Y%m%d')}.json"
            )
            
            if backup_path:
                shutil.copy2(self.data_file, backup_path)
                messagebox.showinfo("نجح", f"تم حفظ النسخة الاحتياطية في:\n{backup_path}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ النسخة الاحتياطية:\n{str(e)}")
    
    def restore_from_file(self):
        """استعادة البيانات من ملف"""
        try:
            # تحذير
            if os.path.exists(self.data_file):
                if not messagebox.askyesno("تحذير", 
                    "سيتم استبدال البيانات الحالية!\nهل تريد المتابعة؟"):
                    return
            
            # اختيار الملف
            restore_path = filedialog.askopenfilename(
                title="اختيار ملف الاستعادة",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if restore_path:
                # التحقق من صحة الملف
                with open(restore_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # نسخ الملف
                shutil.copy2(restore_path, self.data_file)
                
                messagebox.showinfo("نجح", "تم استعادة البيانات بنجاح!")
                self.update_info()
            
        except json.JSONDecodeError:
            messagebox.showerror("خطأ", "الملف المحدد ليس ملف بيانات صحيح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في استعادة البيانات:\n{str(e)}")
    
    def show_backups(self):
        """عرض النسخ الاحتياطية المتاحة"""
        backups_window = tk.Toplevel(self.root)
        backups_window.title("📋 النسخ الاحتياطية المتاحة")
        backups_window.geometry("600x400")
        backups_window.configure(bg='white')
        
        # قائمة النسخ
        tk.Label(backups_window, text="📋 النسخ الاحتياطية المتاحة", 
                font=('Arial', 16, 'bold'), bg='white').pack(pady=10)
        
        listbox_frame = tk.Frame(backups_window, bg='white')
        listbox_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        listbox = tk.Listbox(listbox_frame, font=('Arial', 11))
        scrollbar = tk.Scrollbar(listbox_frame, orient='vertical', command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)
        
        listbox.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # تحميل النسخ
        try:
            backup_files = [f for f in os.listdir(self.backup_folder) if f.endswith('.json')]
            backup_files.sort(reverse=True)  # الأحدث أولاً
            
            for backup_file in backup_files:
                backup_path = os.path.join(self.backup_folder, backup_file)
                file_size = os.path.getsize(backup_path)
                mod_time = datetime.fromtimestamp(os.path.getmtime(backup_path))
                
                display_text = f"{backup_file} - {file_size} بايت - {mod_time.strftime('%Y-%m-%d %H:%M')}"
                listbox.insert(tk.END, display_text)
            
            if not backup_files:
                listbox.insert(tk.END, "لا توجد نسخ احتياطية")
        
        except Exception as e:
            listbox.insert(tk.END, f"خطأ في قراءة النسخ: {str(e)}")
        
        # أزرار
        buttons_frame = tk.Frame(backups_window, bg='white')
        buttons_frame.pack(pady=10)
        
        def restore_selected():
            selection = listbox.curselection()
            if selection:
                selected_file = backup_files[selection[0]]
                backup_path = os.path.join(self.backup_folder, selected_file)
                
                if messagebox.askyesno("تأكيد", f"استعادة من:\n{selected_file}"):
                    try:
                        shutil.copy2(backup_path, self.data_file)
                        messagebox.showinfo("نجح", "تم استعادة البيانات!")
                        backups_window.destroy()
                        self.update_info()
                    except Exception as e:
                        messagebox.showerror("خطأ", f"فشل في الاستعادة:\n{str(e)}")
            else:
                messagebox.showwarning("تحذير", "يرجى اختيار نسخة احتياطية")
        
        tk.Button(buttons_frame, text="🔄 استعادة المحدد", 
                 font=('Arial', 12, 'bold'), bg='#27ae60', fg='white',
                 command=restore_selected).pack(side='left', padx=10)
        
        tk.Button(buttons_frame, text="❌ إغلاق", 
                 font=('Arial', 12, 'bold'), bg='#95a5a6', fg='white',
                 command=backups_window.destroy).pack(side='left', padx=10)
    
    def clear_all_data(self):
        """مسح جميع البيانات"""
        if messagebox.askyesno("تحذير خطير", 
            "⚠️ سيتم مسح جميع البيانات نهائياً!\n\nهل أنت متأكد؟"):
            
            if messagebox.askyesno("تأكيد نهائي", 
                "❌ هذا الإجراء لا يمكن التراجع عنه!\n\nهل تريد المتابعة فعلاً؟"):
                
                try:
                    if os.path.exists(self.data_file):
                        os.remove(self.data_file)
                    
                    messagebox.showinfo("تم", "تم مسح جميع البيانات")
                    self.update_info()
                    
                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في مسح البيانات:\n{str(e)}")
    
    def export_report(self):
        """تصدير تقرير شامل"""
        try:
            if not os.path.exists(self.data_file):
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير!")
                return
            
            # اختيار مكان الحفظ
            report_path = filedialog.asksaveasfilename(
                title="حفظ التقرير الشامل",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                initialname=f"تقرير_شامل_{datetime.now().strftime('%Y%m%d')}.txt"
            )
            
            if report_path:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # إنشاء التقرير
                report = self.generate_comprehensive_report(data)
                
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write(report)
                
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{report_path}")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير:\n{str(e)}")
    
    def generate_comprehensive_report(self, data):
        """إنشاء تقرير شامل"""
        report = f"""
╔══════════════════════════════════════════════════════════════╗
║                    🍦 كاشير الآيس كريم 🍦                    ║
║                      التقرير الشامل                         ║
╚══════════════════════════════════════════════════════════════╝

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
💰 العملة: الريال العماني (ر.ع)

📊 الإحصائيات العامة:
═══════════════════════════════════════════════════════════════
• إجمالي المبيعات: {data.get('total_sales', 0):.3f} ر.ع
• إجمالي المصروفات: {data.get('total_expenses', 0):.3f} ر.ع
• صافي الربح: {(data.get('total_sales', 0) - data.get('total_expenses', 0)):.3f} ر.ع
• عدد الفواتير: {len(data.get('sales_history', []))}

🧾 تفاصيل جميع المبيعات:
═══════════════════════════════════════════════════════════════
"""
        
        sales_history = data.get('sales_history', [])
        for i, sale in enumerate(sales_history, 1):
            items_text = ", ".join([f"{item['name']} ×{item['quantity']}" for item in sale['items']])
            report += f"{i}. {sale['date']} - {sale['total']:.3f} ر.ع\n   العناصر: {items_text}\n\n"
        
        report += "═══════════════════════════════════════════════════════════════\n"
        report += "تم إنشاء هذا التقرير بواسطة برنامج كاشير الآيس كريم 🍦\n"
        report += "═══════════════════════════════════════════════════════════════"
        
        return report
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = BackupManager()
    app.run()
