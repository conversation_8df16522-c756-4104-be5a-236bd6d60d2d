@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                   دليل تثبيت Python الصحيح                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 هذا الملف سيساعدك في تثبيت Python بشكل صحيح
echo.

echo 📋 الخطوات المطلوبة:
echo ═══════════════════════════════════════════════════════════════
echo.

echo 1️⃣ إزالة Python الحالي (المعطل):
echo    • اذهب إلى Control Panel ^> Programs ^> Uninstall a program
echo    • ابحث عن Python واحذفه
echo    • أعد تشغيل الكمبيوتر
echo.

echo 2️⃣ تحميل Python الجديد:
echo    • اذهب إلى: https://www.python.org/downloads/
echo    • حمل أحدث إصدار (Python 3.11 أو 3.12)
echo    • احفظ الملف على سطح المكتب
echo.

echo 3️⃣ تثبيت Python بالطريقة الصحيحة:
echo    • انقر نقراً مزدوجاً على ملف التثبيت
echo    • ✅ تأكد من تحديد: "Add Python to PATH"
echo    • ✅ تأكد من تحديد: "Install pip"
echo    • ✅ اختر: "Customize installation"
echo    • ✅ تأكد من تحديد: "tkinter/Tk"
echo    • ✅ تأكد من تحديد: "pip"
echo    • ✅ تأكد من تحديد: "py launcher"
echo    • انقر Install
echo.

echo 4️⃣ التحقق من التثبيت:
echo    • أعد تشغيل الكمبيوتر
echo    • افتح موجه الأوامر (Command Prompt)
echo    • اكتب: python --version
echo    • يجب أن يظهر رقم الإصدار
echo    • اكتب: pip --version
echo    • يجب أن يظهر رقم إصدار pip
echo.

echo 5️⃣ تجربة البرنامج:
echo    • انقر على: تشغيل_مستقل.bat
echo    • يجب أن يعمل البرنامج بدون مشاكل
echo.

echo ═══════════════════════════════════════════════════════════════
echo 💡 نصائح مهمة:
echo ═══════════════════════════════════════════════════════════════
echo.

echo ✅ تأكد من الاتصال بالإنترنت أثناء التثبيت
echo ✅ أغلق جميع البرامج قبل التثبيت
echo ✅ شغل ملف التثبيت كمدير (Run as Administrator)
echo ✅ لا تغير مجلد التثبيت الافتراضي
echo ✅ انتظر حتى انتهاء التثبيت تماماً
echo.

echo ═══════════════════════════════════════════════════════════════
echo 🔄 بدائل أخرى:
echo ═══════════════════════════════════════════════════════════════
echo.

echo 1️⃣ Anaconda (الأسهل):
echo    • حمل من: https://www.anaconda.com/
echo    • يأتي مع Python وجميع المكتبات
echo    • تثبيت واحد يحل جميع المشاكل
echo.

echo 2️⃣ Python من Microsoft Store:
echo    • افتح Microsoft Store
echo    • ابحث عن "Python"
echo    • ثبت Python 3.11 أو 3.12
echo.

echo 3️⃣ WinPython (محمول):
echo    • حمل من: https://winpython.github.io/
echo    • لا يحتاج تثبيت
echo    • فك الضغط واستخدم
echo.

echo ═══════════════════════════════════════════════════════════════
echo 🚨 إذا استمرت المشاكل:
echo ═══════════════════════════════════════════════════════════════
echo.

echo 1️⃣ استخدم النسخة التجريبية:
echo    👉 انقر على: ice_cream_cashier.html
echo    • تعمل فوراً في المتصفح
echo    • جميع المميزات متاحة
echo.

echo 2️⃣ اطلب المساعدة:
echo    • أرسل صورة من رسالة الخطأ
echo    • اذكر نوع نظام التشغيل
echo    • اذكر إصدار Python المثبت
echo.

echo ═══════════════════════════════════════════════════════════════
echo.

echo هل تريد فتح موقع تحميل Python الآن؟ (y/n)
set /p choice="اختر (y للنعم، n للا): "

if /i "%choice%"=="y" (
    echo 🌐 جاري فتح موقع Python...
    start https://www.python.org/downloads/
    echo ✅ تم فتح الموقع في المتصفح
) else (
    echo ℹ️ يمكنك زيارة الموقع لاحقاً: https://www.python.org/downloads/
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🍦 بعد تثبيت Python، ستتمكن من:
echo ═══════════════════════════════════════════════════════════════
echo.
echo ✅ تشغيل تطبيق سطح المكتب
echo ✅ إنشاء ملف تنفيذي (.exe)
echo ✅ توزيع البرنامج على أجهزة أخرى
echo ✅ إضافة مميزات جديدة
echo ✅ تخصيص البرنامج حسب احتياجاتك
echo.

echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
echo.
pause
