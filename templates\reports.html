{% extends "base.html" %}

{% block title %}التقارير والإحصائيات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h2>
    <div class="btn-group">
        <button class="btn btn-outline-primary" onclick="printReport()">
            <i class="fas fa-print"></i> طباعة
        </button>
        <button class="btn btn-outline-success" onclick="exportReport()">
            <i class="fas fa-download"></i> تصدير
        </button>
    </div>
</div>

<!-- تقارير اليوم -->
<div class="row mb-4">
    <div class="col-12">
        <h4><i class="fas fa-calendar-day"></i> تقرير اليوم</h4>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(today_sales) }}</h4>
                <small>مبيعات اليوم (ج.م)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <i class="fas fa-receipt fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(today_expenses) }}</h4>
                <small>مصروفات اليوم (ج.م)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-{{ 'success' if today_profit >= 0 else 'warning' }} text-white">
            <div class="card-body text-center">
                <i class="fas fa-{{ 'chart-line' if today_profit >= 0 else 'chart-line-down' }} fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(today_profit) }}</h4>
                <small>{{ 'ربح' if today_profit >= 0 else 'خسارة' }} اليوم (ج.م)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <i class="fas fa-percentage fa-2x mb-2"></i>
                <h4>
                    {% if today_sales > 0 %}
                        {{ "%.1f"|format((today_profit / today_sales) * 100) }}%
                    {% else %}
                        0.0%
                    {% endif %}
                </h4>
                <small>هامش الربح اليوم</small>
            </div>
        </div>
    </div>
</div>

<!-- تقارير الشهر -->
<div class="row mb-4">
    <div class="col-12">
        <h4><i class="fas fa-calendar-alt"></i> تقرير الشهر الحالي</h4>
    </div>
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(month_sales) }}</h4>
                <small>مبيعات الشهر (ج.م)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                <i class="fas fa-receipt fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(month_expenses) }}</h4>
                <small>مصروفات الشهر (ج.م)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-{{ 'success' if month_profit >= 0 else 'danger' }} text-white">
            <div class="card-body text-center">
                <i class="fas fa-{{ 'trophy' if month_profit >= 0 else 'exclamation-triangle' }} fa-2x mb-2"></i>
                <h4>{{ "%.2f"|format(month_profit) }}</h4>
                <small>{{ 'ربح' if month_profit >= 0 else 'خسارة' }} الشهر (ج.م)</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <i class="fas fa-calculator fa-2x mb-2"></i>
                <h4>
                    {% if month_sales > 0 %}
                        {{ "%.1f"|format((month_profit / month_sales) * 100) }}%
                    {% else %}
                        0.0%
                    {% endif %}
                </h4>
                <small>هامش الربح الشهري</small>
            </div>
        </div>
    </div>
</div>

<!-- تحليل مفصل -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-chart-pie"></i> تحليل الأداء</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6">
                        <h6>نسبة الربح إلى المبيعات:</h6>
                        {% if month_sales > 0 %}
                            {% set profit_ratio = (month_profit / month_sales) * 100 %}
                            <div class="progress mb-3">
                                <div class="progress-bar bg-{{ 'success' if profit_ratio > 30 else 'warning' if profit_ratio > 10 else 'danger' }}" 
                                     style="width: {{ profit_ratio if profit_ratio > 0 else 0 }}%">
                                    {{ "%.1f"|format(profit_ratio) }}%
                                </div>
                            </div>
                        {% else %}
                            <div class="progress mb-3">
                                <div class="progress-bar bg-secondary" style="width: 0%">0%</div>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-6">
                        <h6>نسبة المصروفات إلى المبيعات:</h6>
                        {% if month_sales > 0 %}
                            {% set expense_ratio = (month_expenses / month_sales) * 100 %}
                            <div class="progress mb-3">
                                <div class="progress-bar bg-{{ 'danger' if expense_ratio > 70 else 'warning' if expense_ratio > 50 else 'success' }}" 
                                     style="width: {{ expense_ratio if expense_ratio <= 100 else 100 }}%">
                                    {{ "%.1f"|format(expense_ratio) }}%
                                </div>
                            </div>
                        {% else %}
                            <div class="progress mb-3">
                                <div class="progress-bar bg-secondary" style="width: 0%">0%</div>
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <hr>
                
                <h6>مؤشرات الأداء:</h6>
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-{{ 'check-circle text-success' if today_profit > 0 else 'times-circle text-danger' }}"></i>
                        {{ 'ربحية اليوم إيجابية' if today_profit > 0 else 'خسارة في اليوم' }}
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-{{ 'check-circle text-success' if month_profit > 0 else 'times-circle text-danger' }}"></i>
                        {{ 'ربحية الشهر إيجابية' if month_profit > 0 else 'خسارة في الشهر' }}
                    </li>
                    <li class="mb-2">
                        {% if month_sales > 0 and (month_profit / month_sales) * 100 > 20 %}
                            <i class="fas fa-check-circle text-success"></i>
                            هامش ربح جيد (أكثر من 20%)
                        {% elif month_sales > 0 and (month_profit / month_sales) * 100 > 10 %}
                            <i class="fas fa-exclamation-triangle text-warning"></i>
                            هامش ربح متوسط (10-20%)
                        {% else %}
                            <i class="fas fa-times-circle text-danger"></i>
                            هامش ربح منخفض (أقل من 10%)
                        {% endif %}
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-lightbulb"></i> توصيات لتحسين الأداء</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-chart-line"></i> نصائح لزيادة الأرباح:</h6>
                    <ul class="mb-0">
                        {% if month_sales > 0 and (month_profit / month_sales) * 100 < 20 %}
                        <li>راجع أسعار المنتجات وتكاليف الإنتاج</li>
                        <li>ابحث عن طرق لتقليل المصروفات</li>
                        {% endif %}
                        
                        {% if today_sales < (month_sales / 30) %}
                        <li>ركز على زيادة المبيعات اليومية</li>
                        <li>فكر في عروض ترويجية</li>
                        {% endif %}
                        
                        <li>راقب المنتجات الأكثر مبيعاً</li>
                        <li>حلل أوقات الذروة في المبيعات</li>
                        <li>احتفظ بسجل دقيق للمصروفات</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> تنبيهات:</h6>
                    <ul class="mb-0">
                        {% if today_profit < 0 %}
                        <li class="text-danger">خسارة في اليوم - راجع المصروفات</li>
                        {% endif %}
                        
                        {% if month_profit < 0 %}
                        <li class="text-danger">خسارة في الشهر - مراجعة عاجلة مطلوبة</li>
                        {% endif %}
                        
                        {% if month_sales > 0 and (month_expenses / month_sales) * 100 > 80 %}
                        <li class="text-warning">المصروفات مرتفعة جداً مقارنة بالمبيعات</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- معلومات إضافية -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5><i class="fas fa-info-circle"></i> معلومات التقرير</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>تاريخ التقرير:</strong> <span id="current-time"></span></p>
                        <p><strong>فترة التقرير:</strong> من بداية الشهر حتى اليوم</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>العملة:</strong> الجنيه المصري (ج.م)</p>
                        <p><strong>نوع التقرير:</strong> تقرير شامل للأرباح والخسائر</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function printReport() {
    window.print();
}

function exportReport() {
    // يمكن تطوير هذه الوظيفة لاحقاً لتصدير التقرير كـ PDF أو Excel
    alert('سيتم تطوير وظيفة التصدير قريباً');
}

// تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleString('ar-EG');
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = timeString;
    }
}

// تحديث الوقت عند تحميل الصفحة وكل دقيقة
document.addEventListener('DOMContentLoaded', updateTime);
setInterval(updateTime, 60000);
</script>

<style>
@media print {
    .btn-group, .navbar, .alert {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        break-inside: avoid;
    }
    
    .bg-primary, .bg-success, .bg-danger, .bg-warning, .bg-info, .bg-secondary {
        background-color: #f8f9fa !important;
        color: #000 !important;
    }
}
</style>
{% endblock %}
