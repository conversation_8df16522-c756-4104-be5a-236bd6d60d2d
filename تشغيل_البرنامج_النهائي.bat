@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                    الريال العماني (ر.ع)                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 جاري البحث عن Python والتحقق من النظام...
echo.

REM البحث عن Python
set PYTHON_FOUND=0
set PYTHON_CMD=""

echo [1/6] فحص python...
python --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    echo ✅ تم العثور على python
    goto :run_program
)

echo [2/6] فحص py launcher...
py --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    echo ✅ تم العثور على py launcher
    goto :run_program
)

echo [3/6] البحث في C:\Python*...
for /d %%d in ("C:\Python*") do (
    if exist "%%d\python.exe" (
        set PYTHON_CMD="%%d\python.exe"
        set PYTHON_FOUND=1
        echo ✅ تم العثور على Python في: %%d
        goto :run_program
    )
)

echo [4/6] البحث في مجلد المستخدم...
for /d %%d in ("C:\Users\<USER>\AppData\Local\Programs\Python\Python*") do (
    if exist "%%d\python.exe" (
        set PYTHON_CMD="%%d\python.exe"
        set PYTHON_FOUND=1
        echo ✅ تم العثور على Python في: %%d
        goto :run_program
    )
)

echo [5/6] البحث في Program Files...
for /d %%d in ("C:\Program Files\Python*") do (
    if exist "%%d\python.exe" (
        set PYTHON_CMD="%%d\python.exe"
        set PYTHON_FOUND=1
        echo ✅ تم العثور على Python في: %%d
        goto :run_program
    )
)

echo [6/6] البحث في Program Files (x86)...
for /d %%d in ("C:\Program Files (x86)\Python*") do (
    if exist "%%d\python.exe" (
        set PYTHON_CMD="%%d\python.exe"
        set PYTHON_FOUND=1
        echo ✅ تم العثور على Python في: %%d
        goto :run_program
    )
)

if %PYTHON_FOUND%==0 goto :no_python

:run_program
echo.
echo 🚀 جاري تشغيل برنامج كاشير الآيس كريم...
echo 💰 العملة: الريال العماني (ر.ع)
echo.

REM تجربة النسخة المحسنة أولاً
if exist "ice_cream_exe.py" (
    echo تشغيل النسخة المحسنة...
    %PYTHON_CMD% ice_cream_exe.py
    if not errorlevel 1 (
        echo ✅ تم تشغيل البرنامج بنجاح!
        goto :success
    )
    echo ❌ فشل في تشغيل النسخة المحسنة
)

REM تجربة النسخة المستقلة
if exist "ice_cream_standalone.py" (
    echo تشغيل النسخة المستقلة...
    %PYTHON_CMD% ice_cream_standalone.py
    if not errorlevel 1 (
        echo ✅ تم تشغيل البرنامج بنجاح!
        goto :success
    )
    echo ❌ فشل في تشغيل النسخة المستقلة
)

REM تجربة النسخة المتقدمة
if exist "desktop_app.py" (
    echo تشغيل النسخة المتقدمة...
    %PYTHON_CMD% desktop_app.py
    if not errorlevel 1 (
        echo ✅ تم تشغيل البرنامج بنجاح!
        goto :success
    )
    echo ❌ فشل في تشغيل النسخة المتقدمة
)

echo ❌ فشل في تشغيل جميع إصدارات البرنامج
goto :try_alternatives

:no_python
echo ❌ لم يتم العثور على Python!
echo.

:try_alternatives
echo.
echo 🔄 جاري تجربة البدائل...
echo.

REM تجربة النسخة التجريبية
if exist "ice_cream_cashier.html" (
    echo 🌐 فتح النسخة التجريبية في المتصفح...
    start "" "ice_cream_cashier.html"
    echo ✅ تم فتح النسخة التجريبية!
    echo.
    echo 💡 النسخة التجريبية تعمل الآن في المتصفح
    echo    • جميع وظائف الكاشير متاحة
    echo    • العملة: الريال العماني (ر.ع)
    echo    • يمكنك إجراء المبيعات والتقارير
    goto :end
)

echo ❌ لم يتم العثور على أي نسخة قابلة للتشغيل!
echo.
echo 💡 الحلول:
echo.
echo 1️⃣ لتطبيق سطح المكتب:
echo    • ثبت Python من: https://www.python.org/downloads/
echo    • تأكد من تحديد "Add Python to PATH"
echo    • أعد تشغيل الكمبيوتر
echo    • أعد تشغيل هذا الملف
echo.
echo 2️⃣ للنسخة التجريبية:
echo    • تأكد من وجود ملف ice_cream_cashier.html
echo    • انقر عليه نقراً مزدوجاً
echo.
echo 3️⃣ للحصول على ملف تنفيذي:
echo    • اتبع التعليمات في ملف "دليل_إنشاء_الملف_التنفيذي.txt"
echo.
goto :end

:success
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🎉 تم تشغيل برنامج كاشير الآيس كريم بنجاح!
echo ═══════════════════════════════════════════════════════════════
echo.
echo 💰 العملة: الريال العماني (ر.ع)
echo 📊 جميع الحسابات بالريال العماني
echo 💾 البيانات تحفظ تلقائياً
echo.

:end
echo.
echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
echo.
pause
