<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>كاشير الآيس كريم 🍦</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .nav {
            background: #f8f9fa;
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        
        .nav-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s;
        }
        
        .nav-btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .nav-btn.active {
            background: #28a745;
        }
        
        .main-content {
            padding: 30px;
        }
        
        .cashier-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .product-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            border-color: #007bff;
            box-shadow: 0 10px 25px rgba(0,123,255,0.2);
        }
        
        .product-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .product-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        
        .product-price {
            font-size: 1.5em;
            color: #28a745;
            font-weight: bold;
        }
        
        .cart {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            height: fit-content;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .cart h3 {
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .cart-item:last-child {
            border-bottom: none;
        }
        
        .quantity-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .qty-btn {
            background: #007bff;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
        }
        
        .qty-btn:hover {
            background: #0056b3;
        }
        
        .total-section {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .checkout-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 18px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s;
        }
        
        .checkout-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40,167,69,0.3);
        }
        
        .checkout-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .clear-btn {
            background: #ffc107;
            color: #212529;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            transition: all 0.3s;
        }
        
        .clear-btn:hover {
            background: #e0a800;
        }
        
        .empty-cart {
            text-align: center;
            color: #6c757d;
            padding: 40px 20px;
        }
        
        .empty-cart i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .section {
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
        }
        
        .stat-card.success {
            background: linear-gradient(45deg, #28a745, #20c997);
        }
        
        .stat-card.warning {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: #212529;
        }
        
        .stat-card.danger {
            background: linear-gradient(45deg, #dc3545, #c82333);
        }
        
        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-ice-cream"></i> كاشير الآيس كريم</h1>
            <p>نظام إدارة محل الآيس كريم المتكامل</p>
        </div>
        
        <div class="nav">
            <button class="nav-btn active" onclick="showSection('cashier')">
                <i class="fas fa-cash-register"></i> الكاشير
            </button>
            <button class="nav-btn" onclick="showSection('products')">
                <i class="fas fa-ice-cream"></i> المنتجات
            </button>
            <button class="nav-btn" onclick="showSection('expenses')">
                <i class="fas fa-receipt"></i> المصروفات
            </button>
            <button class="nav-btn" onclick="showSection('reports')">
                <i class="fas fa-chart-bar"></i> التقارير
            </button>
        </div>
        
        <div class="main-content">
            <!-- قسم الكاشير -->
            <div id="cashier" class="section active">
                <div class="cashier-section">
                    <div>
                        <h3><i class="fas fa-ice-cream"></i> المنتجات المتاحة</h3>
                        <div class="products-grid" id="products-grid">
                            <!-- سيتم تحميل المنتجات هنا -->
                        </div>
                    </div>
                    
                    <div>
                        <div class="cart">
                            <h3><i class="fas fa-shopping-cart"></i> سلة المشتريات</h3>
                            <div id="cart-items">
                                <div class="empty-cart">
                                    <i class="fas fa-shopping-cart"></i>
                                    <p>السلة فارغة</p>
                                </div>
                            </div>
                            
                            <div id="cart-total" style="display: none;">
                                <div class="total-section">
                                    <h4>الإجمالي: <span id="total-amount">0.000</span> ر.ع</h4>
                                </div>
                                <button class="checkout-btn" onclick="checkout()">
                                    <i class="fas fa-check"></i> إتمام البيع
                                </button>
                                <button class="clear-btn" onclick="clearCart()">
                                    <i class="fas fa-trash"></i> مسح السلة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- قسم المنتجات -->
            <div id="products" class="section">
                <h3><i class="fas fa-ice-cream"></i> إدارة المنتجات</h3>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    هذا عرض تجريبي للبرنامج. في النسخة الكاملة يمكنك إضافة وتعديل المنتجات.
                </div>
                <div class="products-grid" id="products-management">
                    <!-- سيتم عرض المنتجات هنا -->
                </div>
            </div>
            
            <!-- قسم المصروفات -->
            <div id="expenses" class="section">
                <h3><i class="fas fa-receipt"></i> إدارة المصروفات</h3>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    هذا عرض تجريبي للبرنامج. في النسخة الكاملة يمكنك إضافة وإدارة المصروفات.
                </div>
                <div class="stats-grid">
                    <div class="stat-card danger">
                        <div class="stat-value">2,450</div>
                        <div class="stat-label">إجمالي المصروفات (ر.ع)</div>
                    </div>
                    <div class="stat-card warning">
                        <div class="stat-value">12</div>
                        <div class="stat-label">عدد المصروفات</div>
                    </div>
                </div>
            </div>
            
            <!-- قسم التقارير -->
            <div id="reports" class="section">
                <h3><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h3>
                
                <h4>تقرير اليوم</h4>
                <div class="stats-grid">
                    <div class="stat-card success">
                        <div class="stat-value" id="today-sales">0</div>
                        <div class="stat-label">مبيعات اليوم (ر.ع)</div>
                    </div>
                    <div class="stat-card danger">
                        <div class="stat-value">15.0</div>
                        <div class="stat-label">مصروفات اليوم (ر.ع)</div>
                    </div>
                    <div class="stat-card" id="profit-card">
                        <div class="stat-value" id="today-profit">0</div>
                        <div class="stat-label">صافي الربح (ر.ع)</div>
                    </div>
                </div>
                
                <div class="alert alert-success">
                    <i class="fas fa-lightbulb"></i>
                    <strong>نصائح لزيادة الأرباح:</strong>
                    <ul style="margin-top: 10px;">
                        <li>راجع أسعار المنتجات بانتظام</li>
                        <li>تابع المنتجات الأكثر مبيعاً</li>
                        <li>قلل من المصروفات غير الضرورية</li>
                        <li>قدم عروض ترويجية في أوقات الذروة</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // بيانات المنتجات التجريبية - الأسعار بالريال العماني
        const products = [
            { id: 1, name: 'آيس كريم فانيليا', price: 1.500, cost: 0.800, icon: '🍦' },
            { id: 2, name: 'آيس كريم شوكولاتة', price: 1.800, cost: 1.000, icon: '🍫' },
            { id: 3, name: 'آيس كريم فراولة', price: 1.600, cost: 0.900, icon: '🍓' },
            { id: 4, name: 'آيس كريم مانجو', price: 2.000, cost: 1.200, icon: '🥭' },
            { id: 5, name: 'آيس كريم كوكيز', price: 2.200, cost: 1.300, icon: '🍪' },
            { id: 6, name: 'آيس كريم كراميل', price: 1.900, cost: 1.100, icon: '🍮' },
        ];

        let cart = [];
        let totalSales = 0;

        // تحميل المنتجات
        function loadProducts() {
            const grid = document.getElementById('products-grid');
            const managementGrid = document.getElementById('products-management');
            
            const productHTML = products.map(product => `
                <div class="product-card" onclick="addToCart(${product.id})">
                    <div class="product-icon">${product.icon}</div>
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${product.price.toFixed(3)} ر.ع</div>
                </div>
            `).join('');
            
            grid.innerHTML = productHTML;
            managementGrid.innerHTML = productHTML;
        }

        // إضافة منتج للسلة
        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            const existingItem = cart.find(item => item.id === productId);
            
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: product.price,
                    quantity: 1
                });
            }
            
            updateCartDisplay();
        }

        // تحديث عرض السلة
        function updateCartDisplay() {
            const cartItemsDiv = document.getElementById('cart-items');
            const cartTotalDiv = document.getElementById('cart-total');
            const totalAmountSpan = document.getElementById('total-amount');
            
            if (cart.length === 0) {
                cartItemsDiv.innerHTML = `
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <p>السلة فارغة</p>
                    </div>
                `;
                cartTotalDiv.style.display = 'none';
                return;
            }
            
            let total = 0;
            cartItemsDiv.innerHTML = cart.map(item => {
                const itemTotal = item.price * item.quantity;
                total += itemTotal;
                return `
                    <div class="cart-item">
                        <div>
                            <strong>${item.name}</strong><br>
                            <small>${item.price.toFixed(3)} ر.ع × ${item.quantity}</small>
                        </div>
                        <div>
                            <div class="quantity-controls">
                                <button class="qty-btn" onclick="updateQuantity(${item.id}, ${item.quantity - 1})">-</button>
                                <span>${item.quantity}</span>
                                <button class="qty-btn" onclick="updateQuantity(${item.id}, ${item.quantity + 1})">+</button>
                            </div>
                            <div><strong>${itemTotal.toFixed(3)} ر.ع</strong></div>
                        </div>
                    </div>
                `;
            }).join('');
            
            totalAmountSpan.textContent = total.toFixed(3);
            cartTotalDiv.style.display = 'block';
        }

        // تحديث الكمية
        function updateQuantity(productId, newQuantity) {
            if (newQuantity <= 0) {
                cart = cart.filter(item => item.id !== productId);
            } else {
                const item = cart.find(item => item.id === productId);
                if (item) item.quantity = newQuantity;
            }
            updateCartDisplay();
        }

        // مسح السلة
        function clearCart() {
            cart = [];
            updateCartDisplay();
        }

        // إتمام البيع
        function checkout() {
            if (cart.length === 0) {
                alert('السلة فارغة!');
                return;
            }
            
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            totalSales += total;
            
            alert(`تم إتمام البيع بنجاح!\nالإجمالي: ${total.toFixed(3)} ر.ع`);
            
            clearCart();
            updateReports();
        }

        // تحديث التقارير
        function updateReports() {
            document.getElementById('today-sales').textContent = totalSales.toFixed(0);
            const profit = totalSales - 150; // خصم المصروفات
            document.getElementById('today-profit').textContent = profit.toFixed(0);
            
            const profitCard = document.getElementById('profit-card');
            if (profit >= 0) {
                profitCard.className = 'stat-card success';
            } else {
                profitCard.className = 'stat-card danger';
            }
        }

        // عرض القسم المحدد
        function showSection(sectionName) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.section').forEach(section => {
                section.classList.remove('active');
            });
            
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // عرض القسم المحدد
            document.getElementById(sectionName).classList.add('active');
            
            // تفعيل الزر المحدد
            event.target.classList.add('active');
        }

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            updateReports();
        });
    </script>
</body>
</html>
