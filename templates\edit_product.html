{% extends "base.html" %}

{% block title %}تعديل المنتج - {{ product.name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-edit"></i> تعديل المنتج: {{ product.name }}</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">اسم المنتج *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ product.name }}" required
                                       placeholder="مثال: آيس كريم فانيليا">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">سعر البيع (ج.م) *</label>
                                <input type="number" class="form-control" id="price" name="price" 
                                       value="{{ product.price }}" step="0.01" min="0" required 
                                       onchange="calculateProfit()">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="cost" class="form-label">سعر التكلفة (ج.م) *</label>
                                <input type="number" class="form-control" id="cost" name="cost" 
                                       value="{{ product.cost }}" step="0.01" min="0" required 
                                       onchange="calculateProfit()">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="profit-margin" class="form-label">هامش الربح</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="profit-margin" readonly
                                           value="{{ '%.1f'|format(product.profit_margin) }}">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"
                                  placeholder="وصف اختياري للمنتج...">{{ product.description or '' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   {{ 'checked' if product.is_active }}>
                            <label class="form-check-label" for="is_active">
                                المنتج نشط (متاح للبيع)
                            </label>
                        </div>
                    </div>
                    
                    <!-- معاينة المنتج -->
                    <div class="card bg-light mb-3">
                        <div class="card-header">
                            <h6><i class="fas fa-eye"></i> معاينة المنتج</h6>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-2 text-center">
                                    <i class="fas fa-ice-cream fa-3x text-primary"></i>
                                </div>
                                <div class="col-md-10">
                                    <h6 id="preview-name">{{ product.name }}</h6>
                                    <p class="mb-1">
                                        <strong>سعر البيع: </strong>
                                        <span id="preview-price" class="badge bg-success">{{ "%.2f"|format(product.price) }} ج.م</span>
                                    </p>
                                    <p class="mb-1">
                                        <strong>سعر التكلفة: </strong>
                                        <span id="preview-cost" class="badge bg-warning">{{ "%.2f"|format(product.cost) }} ج.م</span>
                                    </p>
                                    <p class="mb-1">
                                        <strong>الربح المتوقع: </strong>
                                        <span id="preview-profit" class="badge bg-info">{{ "%.2f"|format(product.price - product.cost) }} ج.م</span>
                                    </p>
                                    <p class="mb-0">
                                        <small id="preview-description" class="text-muted">{{ product.description or 'لا يوجد وصف' }}</small>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="card bg-info text-white mb-3">
                        <div class="card-body">
                            <h6><i class="fas fa-info-circle"></i> معلومات المنتج</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>رقم المنتج:</strong> {{ product.id }}</small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>تاريخ الإضافة:</strong> {{ product.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('products') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للمنتجات
                        </a>
                        <div>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> حفظ التعديلات
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function calculateProfit() {
    const price = parseFloat(document.getElementById('price').value) || 0;
    const cost = parseFloat(document.getElementById('cost').value) || 0;
    
    let margin = 0;
    let profit = 0;
    
    if (cost > 0) {
        profit = price - cost;
        margin = (profit / cost) * 100;
    }
    
    document.getElementById('profit-margin').value = margin.toFixed(1);
    
    // تحديث المعاينة
    updatePreview();
}

function updatePreview() {
    const name = document.getElementById('name').value || 'اسم المنتج';
    const price = parseFloat(document.getElementById('price').value) || 0;
    const cost = parseFloat(document.getElementById('cost').value) || 0;
    const description = document.getElementById('description').value || 'لا يوجد وصف';
    const profit = price - cost;
    
    document.getElementById('preview-name').textContent = name;
    document.getElementById('preview-price').textContent = price.toFixed(2) + ' ج.م';
    document.getElementById('preview-cost').textContent = cost.toFixed(2) + ' ج.م';
    document.getElementById('preview-profit').textContent = profit.toFixed(2) + ' ج.م';
    document.getElementById('preview-description').textContent = description;
}

// ربط الأحداث
document.getElementById('name').addEventListener('input', updatePreview);
document.getElementById('price').addEventListener('input', updatePreview);
document.getElementById('cost').addEventListener('input', updatePreview);
document.getElementById('description').addEventListener('input', updatePreview);
</script>
{% endblock %}
