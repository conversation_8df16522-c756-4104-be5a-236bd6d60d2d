{% extends "base.html" %}

{% block title %}إضافة مصروف جديد{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5><i class="fas fa-plus"></i> إضافة مصروف جديد</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="description" class="form-label">وصف المصروف *</label>
                                <input type="text" class="form-control" id="description" name="description" required
                                       placeholder="مثال: فاتورة كهرباء، مواد خام، راتب موظف...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="amount" class="form-label">المبلغ (ج.م) *</label>
                                <input type="number" class="form-control" id="amount" name="amount" 
                                       step="0.01" min="0" required placeholder="0.00">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="category" class="form-label">فئة المصروف *</label>
                        <select class="form-select" id="category" name="category" required>
                            <option value="">اختر فئة المصروف</option>
                            <option value="مواد خام">مواد خام</option>
                            <option value="رواتب">رواتب</option>
                            <option value="إيجار">إيجار</option>
                            <option value="كهرباء">كهرباء</option>
                            <option value="مياه">مياه</option>
                            <option value="غاز">غاز</option>
                            <option value="صيانة">صيانة</option>
                            <option value="تنظيف">تنظيف</option>
                            <option value="مواصلات">مواصلات</option>
                            <option value="تسويق">تسويق</option>
                            <option value="أدوات">أدوات ومعدات</option>
                            <option value="ضرائب">ضرائب ورسوم</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    
                    <!-- إضافة فئة مخصصة -->
                    <div class="mb-3" id="custom-category-div" style="display: none;">
                        <label for="custom-category" class="form-label">فئة مخصصة</label>
                        <input type="text" class="form-control" id="custom-category" 
                               placeholder="أدخل اسم الفئة الجديدة">
                    </div>
                    
                    <!-- معاينة المصروف -->
                    <div class="card bg-light mb-3">
                        <div class="card-header">
                            <h6><i class="fas fa-eye"></i> معاينة المصروف</h6>
                        </div>
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-2 text-center">
                                    <i class="fas fa-receipt fa-3x text-danger"></i>
                                </div>
                                <div class="col-md-10">
                                    <h6 id="preview-description" class="text-muted">وصف المصروف</h6>
                                    <p class="mb-1">
                                        <strong>المبلغ: </strong>
                                        <span id="preview-amount" class="badge bg-danger">0.00 ج.م</span>
                                    </p>
                                    <p class="mb-0">
                                        <strong>الفئة: </strong>
                                        <span id="preview-category" class="badge bg-secondary">غير محدد</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- نصائح -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> نصائح لتسجيل المصروفات:</h6>
                        <ul class="mb-0">
                            <li>كن دقيقاً في وصف المصروف لسهولة المراجعة لاحقاً</li>
                            <li>اختر الفئة المناسبة لتسهيل التحليل والتقارير</li>
                            <li>احتفظ بالفواتير والإيصالات كمرجع</li>
                            <li>سجل المصروفات فور حدوثها لتجنب النسيان</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('expenses') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> العودة للمصروفات
                        </a>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-save"></i> حفظ المصروف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function updatePreview() {
    const description = document.getElementById('description').value || 'وصف المصروف';
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const category = document.getElementById('category').value || 'غير محدد';
    
    document.getElementById('preview-description').textContent = description;
    document.getElementById('preview-amount').textContent = amount.toFixed(2) + ' ج.م';
    document.getElementById('preview-category').textContent = category;
}

// إظهار/إخفاء حقل الفئة المخصصة
document.getElementById('category').addEventListener('change', function() {
    const customDiv = document.getElementById('custom-category-div');
    const customInput = document.getElementById('custom-category');
    
    if (this.value === 'أخرى') {
        customDiv.style.display = 'block';
        customInput.required = true;
    } else {
        customDiv.style.display = 'none';
        customInput.required = false;
        customInput.value = '';
    }
    
    updatePreview();
});

// تحديث الفئة عند كتابة فئة مخصصة
document.getElementById('custom-category').addEventListener('input', function() {
    if (this.value) {
        document.getElementById('preview-category').textContent = this.value;
    }
});

// ربط الأحداث
document.getElementById('description').addEventListener('input', updatePreview);
document.getElementById('amount').addEventListener('input', updatePreview);
document.getElementById('category').addEventListener('change', updatePreview);

// تحديث المعاينة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', updatePreview);

// تحديث قيمة الفئة قبل الإرسال
document.querySelector('form').addEventListener('submit', function(e) {
    const categorySelect = document.getElementById('category');
    const customCategory = document.getElementById('custom-category');
    
    if (categorySelect.value === 'أخرى' && customCategory.value) {
        categorySelect.value = customCategory.value;
    }
});
</script>
{% endblock %}
