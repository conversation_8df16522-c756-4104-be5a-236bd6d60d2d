@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                   إنشاء Setup 1.0.0.exe                    ║
echo ║                   الريال العماني (ر.ع)                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎯 هذا الملف سينشئ ملف تثبيت احترافي: Setup 1.0.0.exe
echo 📦 ملف التثبيت سيعمل على أي جهاز Windows بدون تثبيت Python
echo.

echo ═══════════════════════════════════════════════════════════════
echo 📋 المتطلبات المطلوبة:
echo ═══════════════════════════════════════════════════════════════
echo.

echo 1️⃣ Python مثبت بشكل صحيح
echo 2️⃣ PyInstaller مثبت
echo 3️⃣ NSIS مثبت (لإنشاء ملف Setup.exe)
echo.

echo ═══════════════════════════════════════════════════════════════
echo 🔍 فحص المتطلبات...
echo ═══════════════════════════════════════════════════════════════
echo.

REM فحص Python
echo [1/3] فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 يرجى تثبيت Python أولاً
    echo 📋 استخدم: تثبيت_Python_الصحيح.bat
    goto :error
)
echo ✅ Python متاح

REM فحص PyInstaller
echo [2/3] فحص PyInstaller...
pyinstaller --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PyInstaller غير مثبت
    echo 🔧 جاري تثبيت PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        echo 💡 جرب: pip install --user pyinstaller
        goto :error
    )
)
echo ✅ PyInstaller متاح

REM فحص NSIS
echo [3/3] فحص NSIS...
set NSIS_FOUND=0

if exist "C:\Program Files (x86)\NSIS\makensis.exe" (
    set NSIS_PATH="C:\Program Files (x86)\NSIS\makensis.exe"
    set NSIS_FOUND=1
    echo ✅ NSIS متاح في Program Files (x86)
) else if exist "C:\Program Files\NSIS\makensis.exe" (
    set NSIS_PATH="C:\Program Files\NSIS\makensis.exe"
    set NSIS_FOUND=1
    echo ✅ NSIS متاح في Program Files
) else (
    makensis.exe /VERSION >nul 2>&1
    if not errorlevel 1 (
        set NSIS_PATH=makensis.exe
        set NSIS_FOUND=1
        echo ✅ NSIS متاح في PATH
    )
)

if %NSIS_FOUND%==0 (
    echo ❌ NSIS غير مثبت
    echo.
    echo 💡 لإنشاء ملف Setup.exe احترافي، تحتاج NSIS:
    echo    • حمل من: https://nsis.sourceforge.io/Download
    echo    • ثبت NSIS وأعد تشغيل هذا الملف
    echo.
    echo 🔄 أم تريد إنشاء ملف .exe بسيط بدون installer؟
    set /p choice="اختر (y للملف البسيط، n للخروج): "
    if /i "%choice%"=="y" goto :simple_exe
    goto :error
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🚀 بدء إنشاء Setup 1.0.0.exe...
echo ═══════════════════════════════════════════════════════════════
echo.

echo [1/5] تشغيل منشئ ملف التثبيت...
python setup_installer.py
if errorlevel 1 (
    echo ❌ فشل في إنشاء ملف التثبيت
    goto :simple_exe
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🎉 تم إنشاء Setup 1.0.0.exe بنجاح!
echo ═══════════════════════════════════════════════════════════════
echo.

echo 📁 ملف التثبيت: Setup_كاشير الآيس كريم_1.0.0.exe
echo 💰 العملة: الريال العماني (ر.ع)
echo.

echo ✅ المميزات:
echo    • ملف تثبيت احترافي
echo    • يعمل على أي جهاز Windows
echo    • لا يحتاج تثبيت Python
echo    • إنشاء اختصارات تلقائياً
echo    • أداة إلغاء تثبيت
echo    • تسجيل في النظام
echo.

echo 💡 يمكنك الآن:
echo    1. توزيع ملف Setup.exe على أي جهاز
echo    2. تشغيل التثبيت بنقرة مزدوجة
echo    3. البرنامج سيظهر في قائمة البرامج
echo.

goto :end

:simple_exe
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🔧 إنشاء ملف .exe بسيط...
echo ═══════════════════════════════════════════════════════════════
echo.

echo [1/3] إنشاء ملف spec...
echo # -*- mode: python ; coding: utf-8 -*- > ice_cream_simple.spec
echo. >> ice_cream_simple.spec
echo block_cipher = None >> ice_cream_simple.spec
echo. >> ice_cream_simple.spec
echo a = Analysis( >> ice_cream_simple.spec
echo     ['كاشير_مستقل_تماما.py'], >> ice_cream_simple.spec
echo     pathex=[], >> ice_cream_simple.spec
echo     binaries=[], >> ice_cream_simple.spec
echo     datas=[('ice_cream_cashier.html', '.'), ('*.txt', '.')], >> ice_cream_simple.spec
echo     hiddenimports=['tkinter', 'tkinter.ttk', 'tkinter.messagebox'], >> ice_cream_simple.spec
echo     hookspath=[], >> ice_cream_simple.spec
echo     hooksconfig={}, >> ice_cream_simple.spec
echo     runtime_hooks=[], >> ice_cream_simple.spec
echo     excludes=['flask', 'jinja2', 'werkzeug'], >> ice_cream_simple.spec
echo     win_no_prefer_redirects=False, >> ice_cream_simple.spec
echo     win_private_assemblies=False, >> ice_cream_simple.spec
echo     cipher=block_cipher, >> ice_cream_simple.spec
echo     noarchive=False, >> ice_cream_simple.spec
echo ^) >> ice_cream_simple.spec
echo. >> ice_cream_simple.spec
echo pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher^) >> ice_cream_simple.spec
echo. >> ice_cream_simple.spec
echo exe = EXE( >> ice_cream_simple.spec
echo     pyz, >> ice_cream_simple.spec
echo     a.scripts, >> ice_cream_simple.spec
echo     a.binaries, >> ice_cream_simple.spec
echo     a.zipfiles, >> ice_cream_simple.spec
echo     a.datas, >> ice_cream_simple.spec
echo     [], >> ice_cream_simple.spec
echo     name='كاشير_الآيس_كريم_v1.0.0', >> ice_cream_simple.spec
echo     debug=False, >> ice_cream_simple.spec
echo     bootloader_ignore_signals=False, >> ice_cream_simple.spec
echo     strip=False, >> ice_cream_simple.spec
echo     upx=True, >> ice_cream_simple.spec
echo     upx_exclude=[], >> ice_cream_simple.spec
echo     runtime_tmpdir=None, >> ice_cream_simple.spec
echo     console=False, >> ice_cream_simple.spec
echo     disable_windowed_traceback=False, >> ice_cream_simple.spec
echo     argv_emulation=False, >> ice_cream_simple.spec
echo     target_arch=None, >> ice_cream_simple.spec
echo     codesign_identity=None, >> ice_cream_simple.spec
echo     entitlements_file=None, >> ice_cream_simple.spec
echo ^) >> ice_cream_simple.spec

echo ✅ تم إنشاء ملف spec

echo [2/3] بناء الملف التنفيذي...
pyinstaller --clean --noconfirm ice_cream_simple.spec
if errorlevel 1 (
    echo ❌ فشل في بناء الملف التنفيذي
    goto :error
)

echo ✅ تم بناء الملف التنفيذي

echo [3/3] إنشاء حزمة التوزيع...
if not exist "IceCream_Package_v1.0.0" mkdir "IceCream_Package_v1.0.0"
if exist "IceCream_Package_v1.0.0\*" del /q "IceCream_Package_v1.0.0\*"

if exist "dist\كاشير_الآيس_كريم_v1.0.0.exe" (
    copy "dist\كاشير_الآيس_كريم_v1.0.0.exe" "IceCream_Package_v1.0.0\"
    copy "ice_cream_cashier.html" "IceCream_Package_v1.0.0\"
    copy "*.txt" "IceCream_Package_v1.0.0\" 2>nul
    
    echo ✅ تم إنشاء حزمة التوزيع
    echo.
    echo ═══════════════════════════════════════════════════════════════
    echo 🎉 تم إنشاء الملف التنفيذي بنجاح!
    echo ═══════════════════════════════════════════════════════════════
    echo.
    echo 📁 حزمة التوزيع: IceCream_Package_v1.0.0
    echo 🚀 الملف التنفيذي: كاشير_الآيس_كريم_v1.0.0.exe
    echo 💰 العملة: الريال العماني (ر.ع)
    echo.
    echo ✅ المميزات:
    echo    • ملف تنفيذي مستقل
    echo    • يعمل على أي جهاز Windows
    echo    • لا يحتاج تثبيت Python
    echo    • حجم صغير ومحسن
    echo.
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    goto :error
)

goto :end

:error
echo.
echo ═══════════════════════════════════════════════════════════════
echo ❌ فشل في إنشاء ملف التثبيت
echo ═══════════════════════════════════════════════════════════════
echo.
echo 💡 الحلول البديلة:
echo.
echo 1️⃣ استخدم النسخة التجريبية:
echo    👉 انقر على: ice_cream_cashier.html
echo.
echo 2️⃣ استخدم النسخة المستقلة:
echo    👉 انقر على: تشغيل_مستقل.bat
echo.
echo 3️⃣ أصلح المتطلبات وأعد المحاولة:
echo    👉 انقر على: تثبيت_Python_الصحيح.bat
echo.
goto :end

:end
echo.
echo ═══════════════════════════════════════════════════════════════
echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
echo ═══════════════════════════════════════════════════════════════
echo.
pause
