# برنامج كاشير الآيس كريم 🍦

برنامج سطح مكتب متكامل لإدارة محل الآيس كريم باستخدام Python و Flask

## المميزات الرئيسية

### 💰 نظام الكاشير
- واجهة سهلة الاستخدام لإدخال المبيعات
- إضافة المنتجات للسلة بنقرة واحدة
- حساب الإجمالي تلقائياً
- إتمام البيع وحفظ الفاتورة

### 🍨 إدارة المنتجات
- إضافة منتجات جديدة
- تعديل أسعار البيع والتكلفة
- حساب هامش الربح تلقائياً
- تفعيل/إلغاء تفعيل المنتجات

### 📊 نظام التقارير
- تقارير المبيعات اليومية والشهرية
- حساب الأرباح والخسائر
- تحليل الأداء المالي
- مؤشرات الأداء والتوصيات

### 💸 إدارة المصروفات
- تسجيل المصروفات اليومية
- تصنيف المصروفات حسب الفئة
- تحليل المصروفات
- متابعة التكاليف

## متطلبات التشغيل

### البرامج المطلوبة
- Python 3.7 أو أحدث
- pip (مدير حزم Python)

### المكتبات المطلوبة
```
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Werkzeug==2.3.7
Jinja2==3.1.2
python-dateutil==2.8.2
```

## طريقة التثبيت والتشغيل

### الطريقة الأولى: استخدام ملف التشغيل التلقائي
1. انقر نقراً مزدوجاً على ملف `run.bat`
2. سيتم تثبيت المتطلبات تلقائياً
3. سيتم تشغيل البرنامج

### الطريقة الثانية: التشغيل اليدوي
1. افتح موجه الأوامر (Command Prompt)
2. انتقل إلى مجلد البرنامج:
   ```
   cd path\to\ice_cream_cashier
   ```
3. ثبت المتطلبات:
   ```
   pip install -r requirements.txt
   ```
4. شغل البرنامج:
   ```
   python app.py
   ```

### الوصول للبرنامج
بعد تشغيل البرنامج، افتح المتصفح واذهب إلى:
```
http://127.0.0.1:5000
```

## استخدام البرنامج

### 1. إضافة المنتجات
- اذهب إلى قسم "المنتجات"
- انقر على "إضافة منتج جديد"
- أدخل اسم المنتج وسعر البيع وسعر التكلفة
- احفظ المنتج

### 2. إجراء عملية بيع
- في الصفحة الرئيسية، انقر على المنتجات لإضافتها للسلة
- استخدم أزرار + و - لتعديل الكمية
- انقر على "إتمام البيع" لحفظ الفاتورة

### 3. تسجيل المصروفات
- اذهب إلى قسم "المصروفات"
- انقر على "إضافة مصروف جديد"
- أدخل وصف المصروف والمبلغ والفئة
- احفظ المصروف

### 4. مراجعة التقارير
- اذهب إلى قسم "التقارير"
- راجع مبيعات اليوم والشهر
- تابع الأرباح والخسائر
- اقرأ التوصيات لتحسين الأداء

## هيكل المشروع

```
ice_cream_cashier/
├── app.py              # الملف الرئيسي للتطبيق
├── models.py           # نماذج قاعدة البيانات
├── requirements.txt    # المتطلبات
├── run.bat            # ملف التشغيل التلقائي
├── README.md          # دليل الاستخدام
└── templates/         # قوالب HTML
    ├── base.html      # القالب الأساسي
    ├── index.html     # الصفحة الرئيسية (الكاشير)
    ├── products.html  # صفحة المنتجات
    ├── add_product.html    # إضافة منتج
    ├── edit_product.html   # تعديل منتج
    ├── expenses.html       # صفحة المصروفات
    ├── add_expense.html    # إضافة مصروف
    └── reports.html        # صفحة التقارير
```

## قاعدة البيانات

يستخدم البرنامج قاعدة بيانات SQLite محلية تحتوي على:

### جدول المنتجات (Product)
- الرقم التعريفي
- اسم المنتج
- سعر البيع
- سعر التكلفة
- الوصف
- حالة التفعيل
- تاريخ الإضافة

### جدول المبيعات (Sale)
- رقم الفاتورة
- إجمالي المبلغ
- تاريخ ووقت البيع

### جدول عناصر الفاتورة (SaleItem)
- رقم العنصر
- رقم الفاتورة
- رقم المنتج
- الكمية
- السعر وقت البيع

### جدول المصروفات (Expense)
- رقم المصروف
- الوصف
- المبلغ
- الفئة
- التاريخ والوقت

## الدعم والمساعدة

### المشاكل الشائعة

**مشكلة: لا يعمل البرنامج**
- تأكد من تثبيت Python بشكل صحيح
- تأكد من تثبيت جميع المتطلبات
- تحقق من رسائل الخطأ في موجه الأوامر

**مشكلة: لا تظهر البيانات**
- تأكد من إضافة منتجات أولاً
- تحقق من اتصال قاعدة البيانات

**مشكلة: خطأ في الحسابات**
- تأكد من إدخال الأسعار بشكل صحيح
- راجع أسعار التكلفة والبيع

### التطوير المستقبلي
- إضافة نظام المستخدمين والصلاحيات
- تصدير التقارير كـ PDF
- نظام النسخ الاحتياطي
- واجهة للهواتف المحمولة
- ربط مع أنظمة الدفع الإلكتروني

---

**تم تطوير هذا البرنامج باستخدام:**
- Python 3
- Flask Framework
- SQLAlchemy ORM
- Bootstrap 5
- Font Awesome Icons

**الترخيص:** مفتوح المصدر للاستخدام التجاري والشخصي
