========================================
       دليل تشغيل برنامج كاشير الآيس كريم
========================================

🍦 مرحباً بك في برنامج كاشير الآيس كريم!

هذا البرنامج يوفر نظام إدارة متكامل لمحل الآيس كريم يشمل:
- نظام كاشير سهل الاستخدام
- إدارة المنتجات والأسعار
- تتبع المبيعات والأرباح
- إدارة المصروفات
- تقارير مالية شاملة

========================================
متطلبات التشغيل:
========================================

1. Python 3.7 أو أحدث
   - يمكن تحميله من: https://www.python.org/downloads/
   - تأكد من تحديد "Add Python to PATH" أثناء التثبيت

2. متصفح ويب (Chrome, Firefox, Edge, Safari)

========================================
طرق التشغيل:
========================================

الطريقة الأولى - النسخة الكاملة (مع Flask):
--------------------------------------------
1. انقر نقراً مزدوجاً على ملف "run.bat"
2. سيتم تثبيت المتطلبات تلقائياً
3. سيتم إنشاء البيانات التجريبية
4. سيفتح المتصفح تلقائياً على http://127.0.0.1:5000

الطريقة الثانية - النسخة المبسطة (بدون مكتبات خارجية):
--------------------------------------------
1. انقر نقراً مزدوجاً على ملف "run_simple.bat"
2. سيفتح المتصفح تلقائياً على http://localhost:8000

الطريقة الثالثة - التشغيل اليدوي:
--------------------------------------------
1. افتح موجه الأوامر (Command Prompt)
2. انتقل لمجلد البرنامج:
   cd "C:\Users\<USER>\Desktop\ickremyaso"
3. للنسخة الكاملة:
   pip install Flask Flask-SQLAlchemy Werkzeug Jinja2 python-dateutil
   python app.py
4. للنسخة المبسطة:
   python simple_app.py

========================================
استخدام البرنامج:
========================================

1. الصفحة الرئيسية (الكاشير):
   - انقر على المنتجات لإضافتها للسلة
   - استخدم أزرار + و - لتعديل الكمية
   - انقر "إتمام البيع" لحفظ الفاتورة

2. إدارة المنتجات:
   - أضف منتجات جديدة مع الأسعار والتكاليف
   - عدل المنتجات الموجودة
   - فعل/ألغ تفعيل المنتجات

3. إدارة المصروفات:
   - سجل المصروفات اليومية
   - صنف المصروفات حسب الفئة
   - راجع إجمالي المصروفات

4. التقارير:
   - راجع مبيعات اليوم والشهر
   - تابع الأرباح والخسائر
   - احصل على توصيات لتحسين الأداء

========================================
حل المشاكل الشائعة:
========================================

مشكلة: "Python غير مثبت"
الحل: ثبت Python من https://www.python.org/downloads/

مشكلة: "فشل في تثبيت المتطلبات"
الحل: جرب النسخة المبسطة أو ثبت المكتبات يدوياً

مشكلة: "لا يفتح المتصفح"
الحل: افتح المتصفح يدوياً واذهب للعنوان المعروض

مشكلة: "خطأ في قاعدة البيانات"
الحل: احذف ملف قاعدة البيانات وأعد تشغيل البرنامج

========================================
الملفات المهمة:
========================================

- app.py: البرنامج الرئيسي (النسخة الكاملة)
- simple_app.py: النسخة المبسطة
- models.py: نماذج قاعدة البيانات
- templates/: ملفات واجهة المستخدم
- run.bat: ملف التشغيل التلقائي (Windows)
- run.sh: ملف التشغيل التلقائي (Linux/Mac)
- README.md: دليل مفصل باللغة الإنجليزية

========================================
الدعم والمساعدة:
========================================

إذا واجهت أي مشاكل:
1. تأكد من تثبيت Python بشكل صحيح
2. جرب النسخة المبسطة أولاً
3. تحقق من رسائل الخطأ في موجه الأوامر
4. تأكد من عدم استخدام منفذ آخر للرقم 5000 أو 8000

========================================
نصائح للاستخدام الأمثل:
========================================

1. أضف منتجاتك الحقيقية قبل البدء
2. سجل المصروفات يومياً
3. راجع التقارير بانتظام
4. احتفظ بنسخة احتياطية من قاعدة البيانات
5. استخدم أسعار دقيقة للحصول على تقارير صحيحة

========================================

شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
نتمنى لك تجارة رابحة ومبيعات ممتازة!
