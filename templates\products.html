{% extends "base.html" %}

{% block title %}إدارة المنتجات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-ice-cream"></i> إدارة المنتجات</h2>
    <a href="{{ url_for('add_product') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة منتج جديد
    </a>
</div>

<div class="card">
    <div class="card-body">
        {% if products %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-primary">
                    <tr>
                        <th>الرقم</th>
                        <th>اسم المنتج</th>
                        <th>سعر البيع</th>
                        <th>سعر التكلفة</th>
                        <th>هامش الربح</th>
                        <th>الوصف</th>
                        <th>الحالة</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in products %}
                    <tr class="{{ 'table-secondary' if not product.is_active }}">
                        <td>{{ product.id }}</td>
                        <td>
                            <strong>{{ product.name }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ "%.3f"|format(product.price) }} ر.ع</span>
                        </td>
                        <td>
                            <span class="badge bg-warning">{{ "%.3f"|format(product.cost) }} ر.ع</span>
                        </td>
                        <td>
                            {% set margin = product.profit_margin %}
                            <span class="badge bg-{{ 'success' if margin > 50 else 'warning' if margin > 20 else 'danger' }}">
                                {{ "%.1f"|format(margin) }}%
                            </span>
                        </td>
                        <td>
                            {% if product.description %}
                                {{ product.description[:50] }}{% if product.description|length > 50 %}...{% endif %}
                            {% else %}
                                <span class="text-muted">لا يوجد وصف</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if product.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ product.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('edit_product', id=product.id) }}" 
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if product.is_active %}
                                <a href="{{ url_for('delete_product', id=product.id) }}" 
                                   class="btn btn-outline-danger" title="حذف"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- إحصائيات سريعة -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>{{ products|selectattr('is_active')|list|length }}</h4>
                        <small>منتجات نشطة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h4>{{ products|rejectattr('is_active')|list|length }}</h4>
                        <small>منتجات غير نشطة</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>{{ "%.2f"|format(products|selectattr('is_active')|map(attribute='price')|sum) }}</h4>
                        <small>إجمالي أسعار البيع</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h4>{{ "%.2f"|format(products|selectattr('is_active')|map(attribute='cost')|sum) }}</h4>
                        <small>إجمالي التكاليف</small>
                    </div>
                </div>
            </div>
        </div>
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-ice-cream fa-4x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد منتجات مضافة بعد</h5>
            <p class="text-muted">ابدأ بإضافة منتجات الآيس كريم الخاصة بك</p>
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة أول منتج
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
