{% extends "base.html" %}

{% block title %}إدارة المصروفات{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-receipt"></i> إدارة المصروفات</h2>
    <a href="{{ url_for('add_expense') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> إضافة مصروف جديد
    </a>
</div>

<div class="row mb-4">
    <!-- إحصائيات سريعة -->
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body text-center">
                <h4>{{ "%.3f"|format(expenses|map(attribute='amount')|sum) }} ر.ع</h4>
                <small>إجمالي المصروفات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <h4>{{ expenses|length }}</h4>
                <small>عدد المصروفات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                {% set categories = expenses|map(attribute='category')|unique|list %}
                <h4>{{ categories|length }}</h4>
                <small>فئات المصروفات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body text-center">
                {% if expenses %}
                    <h4>{{ "%.3f"|format(expenses|map(attribute='amount')|sum / expenses|length) }} ر.ع</h4>
                    <small>متوسط المصروف</small>
                {% else %}
                    <h4>0.000 ر.ع</h4>
                    <small>متوسط المصروف</small>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if expenses %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-danger">
                    <tr>
                        <th>الرقم</th>
                        <th>الوصف</th>
                        <th>المبلغ</th>
                        <th>الفئة</th>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                    </tr>
                </thead>
                <tbody>
                    {% for expense in expenses %}
                    <tr>
                        <td>{{ expense.id }}</td>
                        <td>
                            <strong>{{ expense.description }}</strong>
                        </td>
                        <td>
                            <span class="badge bg-danger">{{ "%.3f"|format(expense.amount) }} ر.ع</span>
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ expense.category }}</span>
                        </td>
                        <td>
                            {{ expense.date.strftime('%Y-%m-%d') }}
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ expense.date.strftime('%H:%M') }}
                            </small>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="2">الإجمالي</th>
                        <th>
                            <span class="badge bg-danger">{{ "%.3f"|format(expenses|map(attribute='amount')|sum) }} ر.ع</span>
                        </th>
                        <th colspan="3"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <!-- تحليل المصروفات حسب الفئة -->
        <div class="row mt-4">
            <div class="col-md-12">
                <h5><i class="fas fa-chart-pie"></i> المصروفات حسب الفئة</h5>
                <div class="row">
                    {% set categories_data = {} %}
                    {% for expense in expenses %}
                        {% if expense.category in categories_data %}
                            {% set _ = categories_data.update({expense.category: categories_data[expense.category] + expense.amount}) %}
                        {% else %}
                            {% set _ = categories_data.update({expense.category: expense.amount}) %}
                        {% endif %}
                    {% endfor %}
                    
                    {% for category, amount in categories_data.items() %}
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h6>{{ category }}</h6>
                                <h4 class="text-danger">{{ "%.3f"|format(amount) }} ر.ع</h4>
                                <small class="text-muted">
                                    {{ "%.1f"|format((amount / expenses|map(attribute='amount')|sum) * 100) }}% من الإجمالي
                                </small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-receipt fa-4x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مصروفات مسجلة بعد</h5>
            <p class="text-muted">ابدأ بتسجيل مصروفات المحل اليومية</p>
            <a href="{{ url_for('add_expense') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة أول مصروف
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
