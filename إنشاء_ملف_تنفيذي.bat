@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                   إنشاء ملف تنفيذي مستقل                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 جاري إعداد بيئة البناء...
echo.

REM البحث عن Python
set PYTHON_FOUND=0

echo [1/5] البحث عن Python...

REM محاولة python
python --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=python
    set PYTHON_FOUND=1
    echo ✅ تم العثور على Python: python
    goto :install_pyinstaller
)

REM محاولة py
py --version >nul 2>&1
if not errorlevel 1 (
    set PYTHON_CMD=py
    set PYTHON_FOUND=1
    echo ✅ تم العثور على Python: py
    goto :install_pyinstaller
)

REM البحث في مجلدات شائعة
for /d %%d in ("C:\Python*") do (
    if exist "%%d\python.exe" (
        set PYTHON_CMD="%%d\python.exe"
        set PYTHON_FOUND=1
        echo ✅ تم العثور على Python في: %%d
        goto :install_pyinstaller
    )
)

for /d %%d in ("C:\Users\<USER>\AppData\Local\Programs\Python\Python*") do (
    if exist "%%d\python.exe" (
        set PYTHON_CMD="%%d\python.exe"
        set PYTHON_FOUND=1
        echo ✅ تم العثور على Python في: %%d
        goto :install_pyinstaller
    )
)

if %PYTHON_FOUND%==0 (
    echo ❌ لم يتم العثور على Python!
    echo.
    echo 💡 يرجى تثبيت Python من: https://www.python.org/downloads/
    echo    تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    pause
    exit /b 1
)

:install_pyinstaller
echo.
echo [2/5] تثبيت PyInstaller...
%PYTHON_CMD% -m pip install pyinstaller
if errorlevel 1 (
    echo ❌ فشل في تثبيت PyInstaller
    echo جاري المحاولة بطريقة أخرى...
    %PYTHON_CMD% -m pip install --user pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        echo.
        echo 💡 جرب تشغيل الأمر التالي يدوياً:
        echo    %PYTHON_CMD% -m pip install pyinstaller
        pause
        exit /b 1
    )
)

echo ✅ تم تثبيت PyInstaller بنجاح
echo.

echo [3/5] إنشاء ملف spec...

REM إنشاء ملف spec مخصص
echo # -*- mode: python ; coding: utf-8 -*- > ice_cream.spec
echo. >> ice_cream.spec
echo block_cipher = None >> ice_cream.spec
echo. >> ice_cream.spec
echo a = Analysis( >> ice_cream.spec
echo     ['ice_cream_exe.py'], >> ice_cream.spec
echo     pathex=[], >> ice_cream.spec
echo     binaries=[], >> ice_cream.spec
echo     datas=[('ice_cream_cashier.html', '.'), ('كيفية_التشغيل.txt', '.')], >> ice_cream.spec
echo     hiddenimports=[], >> ice_cream.spec
echo     hookspath=[], >> ice_cream.spec
echo     hooksconfig={}, >> ice_cream.spec
echo     runtime_hooks=[], >> ice_cream.spec
echo     excludes=[], >> ice_cream.spec
echo     win_no_prefer_redirects=False, >> ice_cream.spec
echo     win_private_assemblies=False, >> ice_cream.spec
echo     cipher=block_cipher, >> ice_cream.spec
echo     noarchive=False, >> ice_cream.spec
echo ) >> ice_cream.spec
echo. >> ice_cream.spec
echo pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher) >> ice_cream.spec
echo. >> ice_cream.spec
echo exe = EXE( >> ice_cream.spec
echo     pyz, >> ice_cream.spec
echo     a.scripts, >> ice_cream.spec
echo     a.binaries, >> ice_cream.spec
echo     a.zipfiles, >> ice_cream.spec
echo     a.datas, >> ice_cream.spec
echo     [], >> ice_cream.spec
echo     name='كاشير_الآيس_كريم', >> ice_cream.spec
echo     debug=False, >> ice_cream.spec
echo     bootloader_ignore_signals=False, >> ice_cream.spec
echo     strip=False, >> ice_cream.spec
echo     upx=True, >> ice_cream.spec
echo     upx_exclude=[], >> ice_cream.spec
echo     runtime_tmpdir=None, >> ice_cream.spec
echo     console=False, >> ice_cream.spec
echo     disable_windowed_traceback=False, >> ice_cream.spec
echo     argv_emulation=False, >> ice_cream.spec
echo     target_arch=None, >> ice_cream.spec
echo     codesign_identity=None, >> ice_cream.spec
echo     entitlements_file=None, >> ice_cream.spec
echo ) >> ice_cream.spec

echo ✅ تم إنشاء ملف spec
echo.

echo [4/5] بناء الملف التنفيذي...
%PYTHON_CMD% -m PyInstaller ice_cream.spec --clean --noconfirm
if errorlevel 1 (
    echo ❌ فشل في بناء الملف التنفيذي
    echo.
    echo 💡 جاري المحاولة بطريقة مبسطة...
    %PYTHON_CMD% -m PyInstaller --onefile --windowed --name "كاشير_الآيس_كريم" ice_cream_exe.py
    if errorlevel 1 (
        echo ❌ فشل في البناء المبسط أيضاً
        pause
        exit /b 1
    )
)

echo ✅ تم بناء الملف التنفيذي بنجاح!
echo.

echo [5/5] إنشاء حزمة التوزيع...

REM إنشاء مجلد التوزيع
if not exist "IceCreamCashier_Package" mkdir "IceCreamCashier_Package"
if exist "IceCreamCashier_Package\*" del /q "IceCreamCashier_Package\*"

REM نسخ الملفات
if exist "dist\كاشير_الآيس_كريم.exe" (
    copy "dist\كاشير_الآيس_كريم.exe" "IceCreamCashier_Package\"
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    pause
    exit /b 1
)

copy "ice_cream_cashier.html" "IceCreamCashier_Package\"
copy "كيفية_التشغيل.txt" "IceCreamCashier_Package\"

REM إنشاء ملف README للحزمة
echo 🍦 برنامج كاشير الآيس كريم > "IceCreamCashier_Package\اقرأني.txt"
echo. >> "IceCreamCashier_Package\اقرأني.txt"
echo ✅ العملة: الريال العماني (ر.ع) >> "IceCreamCashier_Package\اقرأني.txt"
echo ✅ ملف تنفيذي مستقل - لا يحتاج تثبيت Python >> "IceCreamCashier_Package\اقرأني.txt"
echo. >> "IceCreamCashier_Package\اقرأني.txt"
echo 🚀 للتشغيل: >> "IceCreamCashier_Package\اقرأني.txt"
echo انقر نقراً مزدوجاً على: كاشير_الآيس_كريم.exe >> "IceCreamCashier_Package\اقرأني.txt"
echo. >> "IceCreamCashier_Package\اقرأني.txt"
echo 📁 الملفات المضمنة: >> "IceCreamCashier_Package\اقرأني.txt"
echo • كاشير_الآيس_كريم.exe - البرنامج الرئيسي >> "IceCreamCashier_Package\اقرأني.txt"
echo • ice_cream_cashier.html - النسخة التجريبية >> "IceCreamCashier_Package\اقرأني.txt"
echo • كيفية_التشغيل.txt - دليل الاستخدام >> "IceCreamCashier_Package\اقرأني.txt"
echo. >> "IceCreamCashier_Package\اقرأني.txt"
echo 💡 يمكنك نسخ هذا المجلد إلى أي جهاز آخر وتشغيله مباشرة! >> "IceCreamCashier_Package\اقرأني.txt"

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🎉 تم إنشاء الملف التنفيذي بنجاح!
echo ═══════════════════════════════════════════════════════════════
echo.
echo 📁 حزمة التوزيع: IceCreamCashier_Package
echo 🚀 الملف التنفيذي: كاشير_الآيس_كريم.exe
echo 💰 العملة: الريال العماني (ر.ع)
echo.
echo ✅ المميزات:
echo    • ملف تنفيذي مستقل
echo    • لا يحتاج تثبيت Python
echo    • يعمل على أي جهاز Windows
echo    • حفظ البيانات تلقائياً
echo    • واجهة عربية كاملة
echo.
echo 💡 يمكنك الآن:
echo    1. نسخ مجلد IceCreamCashier_Package إلى أي جهاز
echo    2. تشغيل كاشير_الآيس_كريم.exe مباشرة
echo    3. توزيع البرنامج على أي عدد من الأجهزة
echo.

pause
