@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                   نسخة Flask - الريال العماني               ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔧 جاري إعداد وتشغيل نسخة Flask...
echo 💰 العملة: الريال العماني (ر.ع)
echo.

echo [1/4] التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 يرجى تثبيت Python أولاً
    echo 📋 استخدم: تثبيت_Python_الصحيح.bat
    pause
    exit /b 1
)
echo ✅ Python متاح

echo [2/4] تثبيت Flask...
pip install flask flask-sqlalchemy >nul 2>&1
if errorlevel 1 (
    echo ❌ فشل في تثبيت Flask
    echo 💡 جاري المحاولة بطريقة أخرى...
    python -m pip install flask flask-sqlalchemy
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        echo 📋 جرب تشغيل الأمر يدوياً: pip install flask flask-sqlalchemy
        pause
        exit /b 1
    )
)
echo ✅ تم تثبيت Flask

echo [3/4] إنشاء قاعدة البيانات...
python -c "from app import app, db; app.app_context().push(); db.create_all(); print('✅ تم إنشاء قاعدة البيانات')"
if errorlevel 1 (
    echo ❌ فشل في إنشاء قاعدة البيانات
    echo 💡 تحقق من ملف app.py
    pause
    exit /b 1
)

echo [4/4] إضافة منتجات تجريبية بالريال العماني...
python -c "
from app import app, db, Product
app.app_context().push()

# التحقق من وجود منتجات
if Product.query.count() == 0:
    products = [
        Product(name='آيس كريم فانيليا', price=1.500, cost=0.800, description='آيس كريم كلاسيكي بطعم الفانيليا'),
        Product(name='آيس كريم شوكولاتة', price=1.800, cost=1.000, description='آيس كريم غني بطعم الشوكولاتة'),
        Product(name='آيس كريم فراولة', price=1.600, cost=0.900, description='آيس كريم منعش بطعم الفراولة'),
        Product(name='آيس كريم مانجو', price=2.000, cost=1.200, description='آيس كريم استوائي بطعم المانجو'),
        Product(name='آيس كريم كوكيز', price=2.200, cost=1.300, description='آيس كريم مع قطع الكوكيز'),
        Product(name='آيس كريم كراميل', price=1.900, cost=1.100, description='آيس كريم حلو بطعم الكراميل'),
        Product(name='آيس كريم فستق', price=2.500, cost=1.500, description='آيس كريم فاخر بطعم الفستق'),
        Product(name='آيس كريم لوز', price=2.300, cost=1.400, description='آيس كريم مميز بطعم اللوز')
    ]
    
    for product in products:
        db.session.add(product)
    
    db.session.commit()
    print('✅ تم إضافة المنتجات التجريبية بالريال العماني')
else:
    print('ℹ️ المنتجات موجودة مسبقاً')
"

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🚀 تشغيل خادم Flask...
echo ═══════════════════════════════════════════════════════════════
echo.
echo 🌐 سيتم فتح البرنامج على: http://127.0.0.1:5000
echo 💰 العملة: الريال العماني (ر.ع)
echo ✅ تم إصلاح خطأ شاشة المصروفات
echo.
echo 💡 للإيقاف: اضغط Ctrl+C
echo.

timeout /t 3 >nul

start http://127.0.0.1:5000

python app.py

echo.
echo ═══════════════════════════════════════════════════════════════
echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
echo ═══════════════════════════════════════════════════════════════
pause
