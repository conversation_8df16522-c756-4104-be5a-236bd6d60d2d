@echo off
chcp 65001 >nul
cls

echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║
echo ║                   إنشاء ملف .exe سريع                      ║
echo ║                   الريال العماني (ر.ع)                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🚀 إنشاء ملف تنفيذي سريع وبسيط
echo 📦 سيعمل على أي جهاز Windows بدون تثبيت Python
echo.

echo ═══════════════════════════════════════════════════════════════
echo 🔍 فحص المتطلبات...
echo ═══════════════════════════════════════════════════════════════
echo.

REM فحص Python
echo [1/2] فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير متاح
    echo 💡 يرجى تثبيت Python أولاً
    echo 📋 استخدم: تثبيت_Python_الصحيح.bat
    pause
    exit /b 1
)
echo ✅ Python متاح

REM فحص PyInstaller
echo [2/2] فحص PyInstaller...
pyinstaller --version >nul 2>&1
if errorlevel 1 (
    echo ❌ PyInstaller غير مثبت
    echo 🔧 جاري تثبيت PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo ❌ فشل في تثبيت PyInstaller
        echo 💡 جرب: pip install --user pyinstaller
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت PyInstaller
) else (
    echo ✅ PyInstaller متاح
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🔨 بناء الملف التنفيذي...
echo ═══════════════════════════════════════════════════════════════
echo.

echo [1/3] تنظيف الملفات القديمة...
if exist "dist" rmdir /s /q "dist" >nul 2>&1
if exist "build" rmdir /s /q "build" >nul 2>&1
if exist "*.spec" del /q "*.spec" >nul 2>&1
echo ✅ تم تنظيف الملفات القديمة

echo [2/3] إنشاء الملف التنفيذي...
pyinstaller ^
    --onefile ^
    --windowed ^
    --name "كاشير_الآيس_كريم_v1.0.0" ^
    --add-data "ice_cream_cashier.html;." ^
    --add-data "*.txt;." ^
    --hidden-import tkinter ^
    --hidden-import tkinter.ttk ^
    --hidden-import tkinter.messagebox ^
    --hidden-import tkinter.simpledialog ^
    --hidden-import tkinter.filedialog ^
    --exclude-module flask ^
    --exclude-module jinja2 ^
    --exclude-module werkzeug ^
    --exclude-module matplotlib ^
    --exclude-module numpy ^
    --exclude-module pandas ^
    --clean ^
    --noconfirm ^
    كاشير_مستقل_تماما.py

if errorlevel 1 (
    echo ❌ فشل في إنشاء الملف التنفيذي
    echo.
    echo 💡 جرب الحلول التالية:
    echo    1. تأكد من وجود ملف كاشير_مستقل_تماما.py
    echo    2. جرب تشغيل البرنامج أولاً: python كاشير_مستقل_تماما.py
    echo    3. تأكد من عمل tkinter: python -m tkinter
    echo.
    pause
    exit /b 1
)

echo ✅ تم إنشاء الملف التنفيذي

echo [3/3] إنشاء حزمة التوزيع...
set PACKAGE_NAME=كاشير_الآيس_كريم_Package_v1.0.0
if exist "%PACKAGE_NAME%" rmdir /s /q "%PACKAGE_NAME%" >nul 2>&1
mkdir "%PACKAGE_NAME%"

REM نسخ الملف التنفيذي
if exist "dist\كاشير_الآيس_كريم_v1.0.0.exe" (
    copy "dist\كاشير_الآيس_كريم_v1.0.0.exe" "%PACKAGE_NAME%\"
    echo ✅ تم نسخ الملف التنفيذي
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    pause
    exit /b 1
)

REM نسخ الملفات الإضافية
if exist "ice_cream_cashier.html" copy "ice_cream_cashier.html" "%PACKAGE_NAME%\" >nul
if exist "دليل_المستخدم_الشامل.txt" copy "دليل_المستخدم_الشامل.txt" "%PACKAGE_NAME%\" >nul
if exist "README_النهائي.txt" copy "README_النهائي.txt" "%PACKAGE_NAME%\" >nul
if exist "إصلاحات_Flask.txt" copy "إصلاحات_Flask.txt" "%PACKAGE_NAME%\" >nul

REM إنشاء ملف تشغيل سريع
echo @echo off > "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo chcp 65001 ^>nul >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo cls >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo echo ╔══════════════════════════════════════════════════════════════╗ >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo echo ║                    🍦 كاشير الآيس كريم 🍦                    ║ >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo echo ║                   الريال العماني (ر.ع)                     ║ >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo echo ╚══════════════════════════════════════════════════════════════╝ >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo echo. >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo echo 🚀 جاري تشغيل برنامج كاشير الآيس كريم... >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo echo 💰 العملة: الريال العماني (ر.ع) >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo echo. >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"
echo start "" "كاشير_الآيس_كريم_v1.0.0.exe" >> "%PACKAGE_NAME%\تشغيل_البرنامج.bat"

REM إنشاء ملف معلومات
echo ╔══════════════════════════════════════════════════════════════╗ > "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo ║                    🍦 كاشير الآيس كريم 🍦                    ║ >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo ║                   حزمة التوزيع v1.0.0                       ║ >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo ║                   الريال العماني (ر.ع)                     ║ >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo ╚══════════════════════════════════════════════════════════════╝ >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo. >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 📦 محتويات الحزمة: >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo ═══════════════════ >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo. >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 🚀 كاشير_الآيس_كريم_v1.0.0.exe - الملف التنفيذي الرئيسي >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 🌐 ice_cream_cashier.html - النسخة التجريبية >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 📋 تشغيل_البرنامج.bat - ملف تشغيل سريع >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 📚 ملفات التوثيق والأدلة >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo. >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 🎯 طريقة التشغيل: >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo ═══════════════════ >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo. >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 1️⃣ انقر نقراً مزدوجاً على: كاشير_الآيس_كريم_v1.0.0.exe >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 2️⃣ أو انقر على: تشغيل_البرنامج.bat >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo 3️⃣ للنسخة التجريبية: انقر على ice_cream_cashier.html >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo. >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo ✅ المميزات: >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo ═══════════════ >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo • يعمل على أي جهاز Windows >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo • لا يحتاج تثبيت Python >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo • واجهة عربية كاملة >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo • العملة بالريال العماني >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo • نظام كاشير متكامل >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo • تقارير مالية مفصلة >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"
echo • نسخ احتياطية آمنة >> "%PACKAGE_NAME%\معلومات_الحزمة.txt"

echo ✅ تم إنشاء حزمة التوزيع

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🎉 تم إنشاء الملف التنفيذي بنجاح!
echo ═══════════════════════════════════════════════════════════════
echo.

echo 📁 حزمة التوزيع: %PACKAGE_NAME%
echo 🚀 الملف التنفيذي: كاشير_الآيس_كريم_v1.0.0.exe
echo 💰 العملة: الريال العماني (ر.ع)
echo.

echo ✅ المميزات:
echo    • ملف تنفيذي مستقل (حوالي 15-25 ميجا)
echo    • يعمل على أي جهاز Windows
echo    • لا يحتاج تثبيت Python أو مكتبات
echo    • واجهة عربية كاملة
echo    • جميع المميزات متاحة
echo.

echo 💡 طرق التشغيل:
echo    1️⃣ انقر نقراً مزدوجاً على الملف التنفيذي
echo    2️⃣ استخدم ملف تشغيل_البرنامج.bat
echo    3️⃣ للنسخة التجريبية: ice_cream_cashier.html
echo.

echo 📦 يمكنك الآن:
echo    • نسخ المجلد على USB
echo    • إرساله بالبريد الإلكتروني
echo    • رفعه على موقعك
echo    • توزيعه على العملاء
echo.

REM فتح مجلد الحزمة
echo 🔍 فتح مجلد الحزمة...
start "" "%PACKAGE_NAME%"

echo.
echo ═══════════════════════════════════════════════════════════════
echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
echo ═══════════════════════════════════════════════════════════════
echo.
pause
