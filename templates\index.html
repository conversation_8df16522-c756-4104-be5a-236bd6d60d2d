{% extends "base.html" %}

{% block title %}كاشير الآيس كريم - الصفحة الرئيسية{% endblock %}

{% block content %}
<div class="row">
    <!-- قائمة المنتجات -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-ice-cream"></i> قائمة المنتجات</h5>
            </div>
            <div class="card-body">
                <div class="row" id="products-grid">
                    {% for product in products %}
                    <div class="col-md-4 col-sm-6 mb-3">
                        <div class="card product-card" onclick="addToCart({{ product.id }}, '{{ product.name }}', {{ product.price }})">
                            <div class="card-body text-center">
                                <i class="fas fa-ice-cream fa-3x text-primary mb-3"></i>
                                <h6 class="card-title">{{ product.name }}</h6>
                                <p class="card-text">
                                    <strong>{{ "%.2f"|format(product.price) }} ج.م</strong>
                                </p>
                                {% if product.description %}
                                <small class="text-muted">{{ product.description }}</small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                {% if not products %}
                <div class="text-center py-5">
                    <i class="fas fa-ice-cream fa-4x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد منتجات متاحة</h5>
                    <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة منتج جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- سلة المشتريات -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-shopping-cart"></i> سلة المشتريات</h5>
            </div>
            <div class="card-body">
                <div id="cart-items">
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>السلة فارغة</p>
                    </div>
                </div>
                
                <div id="cart-total" class="total-section mt-3" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">الإجمالي:</h5>
                        <h4 class="mb-0" id="total-amount">0.00 ج.م</h4>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-success w-100" id="checkout-btn" onclick="checkout()" disabled>
                        <i class="fas fa-check"></i> إتمام البيع
                    </button>
                    <button class="btn btn-warning w-100 mt-2" onclick="clearCart()">
                        <i class="fas fa-trash"></i> مسح السلة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let cart = [];
let total = 0;

function addToCart(productId, productName, productPrice) {
    // البحث عن المنتج في السلة
    let existingItem = cart.find(item => item.product_id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            product_id: productId,
            name: productName,
            price: productPrice,
            quantity: 1
        });
    }
    
    updateCartDisplay();
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.product_id !== productId);
    updateCartDisplay();
}

function updateQuantity(productId, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(productId);
        return;
    }
    
    let item = cart.find(item => item.product_id === productId);
    if (item) {
        item.quantity = newQuantity;
        updateCartDisplay();
    }
}

function updateCartDisplay() {
    const cartItemsDiv = document.getElementById('cart-items');
    const cartTotalDiv = document.getElementById('cart-total');
    const checkoutBtn = document.getElementById('checkout-btn');
    const totalAmountSpan = document.getElementById('total-amount');
    
    if (cart.length === 0) {
        cartItemsDiv.innerHTML = `
            <div class="text-center text-muted py-4">
                <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        cartTotalDiv.style.display = 'none';
        checkoutBtn.disabled = true;
        return;
    }
    
    total = 0;
    let cartHTML = '';
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        cartHTML += `
            <div class="cart-item">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">${item.price.toFixed(2)} ج.م × ${item.quantity}</small>
                    </div>
                    <div class="text-end">
                        <div class="btn-group btn-group-sm mb-1">
                            <button class="btn btn-outline-secondary" onclick="updateQuantity(${item.product_id}, ${item.quantity - 1})">-</button>
                            <span class="btn btn-outline-secondary">${item.quantity}</span>
                            <button class="btn btn-outline-secondary" onclick="updateQuantity(${item.product_id}, ${item.quantity + 1})">+</button>
                        </div>
                        <div>
                            <strong>${itemTotal.toFixed(2)} ج.م</strong>
                            <button class="btn btn-sm btn-outline-danger ms-1" onclick="removeFromCart(${item.product_id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    cartItemsDiv.innerHTML = cartHTML;
    totalAmountSpan.textContent = total.toFixed(2) + ' ج.م';
    cartTotalDiv.style.display = 'block';
    checkoutBtn.disabled = false;
}

function clearCart() {
    cart = [];
    updateCartDisplay();
}

function checkout() {
    if (cart.length === 0) {
        alert('السلة فارغة!');
        return;
    }
    
    const saleData = {
        items: cart,
        total: total
    };
    
    fetch('/add_sale', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(saleData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('تم إتمام البيع بنجاح!\nرقم الفاتورة: ' + data.sale_id);
            clearCart();
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    });
}
</script>
{% endblock %}
