@echo off
chcp 65001 >nul
echo ========================================
echo    برنامج كاشير الآيس كريم - سطح المكتب 🍦
echo ========================================
echo.
echo تطبيق سطح مكتب باستخدام Tkinter
echo لا يحتاج مكتبات خارجية - يعمل مع Python فقط
echo.

echo جاري التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت! يرجى تثبيت Python أولاً
    echo يمكنك تحميله من: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python متاح
echo.

echo جاري تشغيل تطبيق سطح المكتب...
echo.

python desktop_app.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo تأكد من تثبيت Python بشكل صحيح
    pause
)

echo.
echo شكراً لاستخدام برنامج كاشير الآيس كريم! 🍦
pause
